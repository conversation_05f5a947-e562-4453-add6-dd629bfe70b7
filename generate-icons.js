const { createCanvas } = require('canvas');
const fs = require('fs');
const path = require('path');

// 确保 icons 目录存在
const iconsDir = path.join(__dirname, 'icons');
if (!fs.existsSync(iconsDir)) {
    fs.mkdirSync(iconsDir);
}

// 生成图标的函数
function generateIcon(size) {
    const canvas = createCanvas(size, size);
    const ctx = canvas.getContext('2d');

    // 设置背景色
    ctx.fillStyle = '#4CAF50';
    ctx.fillRect(0, 0, size, size);

    // 绘制圆形
    ctx.beginPath();
    ctx.arc(size/2, size/2, size/3, 0, Math.PI * 2);
    ctx.fillStyle = '#ffffff';
    ctx.fill();

    // 绘制 "F" 字母
    ctx.fillStyle = '#4CAF50';
    ctx.font = `bold ${size/2}px Arial`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText('F', size/2, size/2);

    // 保存为 PNG
    const buffer = canvas.toBuffer('image/png');
    fs.writeFileSync(path.join(iconsDir, `icon${size}.png`), buffer);
}

// 生成不同尺寸的图标
[16, 48, 128].forEach(size => {
    generateIcon(size);
    console.log(`Generated ${size}x${size} icon`);
}); 