#!/bin/bash

# 设置变量
EXTENSION_NAME="feiyu-product-selector"
VERSION="1.0"
BUILD_DIR="out"
DIST_DIR="dist"

# 创建必要的目录
mkdir -p $BUILD_DIR
mkdir -p $DIST_DIR

# 复制文件到构建目录
echo "Copying files to build directory..."
cp manifest.json $BUILD_DIR/
cp popup.html $BUILD_DIR/
cp popup.js $BUILD_DIR/
cp content.js $BUILD_DIR/
cp background.js $BUILD_DIR/
cp styles.css $BUILD_DIR/
cp README.md $BUILD_DIR/

# 创建图标目录
mkdir -p $BUILD_DIR/icons

# 检查图标文件是否存在
if [ ! -f "icons/icon16.png" ] || [ ! -f "icons/icon48.png" ] || [ ! -f "icons/icon128.png" ]; then
    echo "Warning: Icon files are missing. Please create icons in the icons directory:"
    echo "- icons/icon16.png (16x16)"
    echo "- icons/icon48.png (48x48)"
    echo "- icons/icon128.png (128x128)"
    exit 1
fi

# 复制图标
cp icons/icon*.png $BUILD_DIR/icons/

# 创建 zip 文件
echo "Creating zip file..."
cd $BUILD_DIR
zip -r ../$DIST_DIR/$EXTENSION_NAME-v$VERSION.zip . -x "*.DS_Store" -x "__MACOSX"

# 清理构建目录
echo "Cleaning up..."
cd ..
#rm -rf $BUILD_DIR

echo "Build completed! Extension package is available at: $DIST_DIR/$EXTENSION_NAME-v$VERSION.zip" 