# Next.js 迁移计划

## 🎯 迁移策略: Next.js Chrome扩展

### 项目结构
```
feiyu-fbm-assistant/
├── src/
│   ├── pages/           # Next.js页面
│   │   ├── popup.tsx    # 弹窗界面
│   │   └── options.tsx  # 设置页面
│   ├── components/      # React组件
│   │   ├── ui/          # 基础UI组件
│   │   ├── charts/      # 图表组件
│   │   └── forms/       # 表单组件
│   ├── hooks/           # 自定义Hooks
│   ├── utils/           # 工具函数
│   ├── types/           # TypeScript类型定义
│   ├── styles/          # 样式文件
│   └── extension/       # 扩展相关
│       ├── content.ts   # Content Script
│       ├── background.ts # Background Script
│       └── manifest.json
├── public/              # 静态资源
├── next.config.js       # Next.js配置
├── webpack.config.js    # 扩展打包配置
└── package.json
```

### 技术栈升级
- **框架**: Next.js 14
- **语言**: TypeScript
- **UI库**: React + Tailwind CSS
- **状态管理**: Zustand
- **图表**: Chart.js + React-Chartjs-2
- **构建**: Webpack + Custom Config
- **测试**: Jest + React Testing Library

### 核心组件设计

#### 1. Popup组件 (popup.tsx)
```typescript
import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { useFBMStore } from '@/hooks/useFBMStore'

export default function Popup() {
  const { 
    products, 
    isScrapingLoading, 
    progress, 
    startScraping, 
    exportProducts 
  } = useFBMStore()

  return (
    <div className="w-80 p-4">
      <Header />
      <Controls />
      <Progress value={progress} />
      <Status />
      <ExportButtons />
      <InfoPanel />
    </div>
  )
}
```

#### 2. 自定义Hooks
```typescript
// hooks/useFBMStore.ts
import { create } from 'zustand'

interface FBMStore {
  products: Product[]
  isLoading: boolean
  progress: number
  startScraping: () => Promise<void>
  exportProducts: (type: 'fbm' | 'all') => void
}

export const useFBMStore = create<FBMStore>((set, get) => ({
  products: [],
  isLoading: false,
  progress: 0,
  startScraping: async () => {
    // 实现抓取逻辑
  },
  exportProducts: (type) => {
    // 实现导出逻辑
  }
}))
```

### 构建配置

#### next.config.js
```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'export',
  trailingSlash: true,
  images: {
    unoptimized: true
  },
  assetPrefix: process.env.NODE_ENV === 'production' ? '.' : '',
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.entry = {
        popup: './src/pages/popup.tsx',
        options: './src/pages/options.tsx',
        content: './src/extension/content.ts',
        background: './src/extension/background.ts'
      }
    }
    return config
  }
}

module.exports = nextConfig
```

### 迁移步骤

#### 阶段1: 基础迁移 (1-2周)
- [ ] 设置Next.js项目结构
- [ ] 迁移现有UI到React组件
- [ ] 配置TypeScript和Tailwind
- [ ] 设置构建流程

#### 阶段2: 功能重构 (2-3周)
- [ ] 重写状态管理逻辑
- [ ] 优化FBM检测算法
- [ ] 添加错误边界和错误处理
- [ ] 实现单元测试

#### 阶段3: 功能增强 (2-4周)
- [ ] 添加数据可视化图表
- [ ] 实现设置页面
- [ ] 添加历史数据功能
- [ ] 性能优化

#### 阶段4: 测试和发布 (1周)
- [ ] 端到端测试
- [ ] 性能测试
- [ ] 用户体验测试
- [ ] 打包和发布

### 优势
1. **现代化开发体验**: React Hooks、TypeScript、热重载
2. **更好的代码组织**: 组件化、模块化
3. **丰富的生态系统**: 大量开源组件和工具
4. **更好的测试支持**: Jest、React Testing Library
5. **更强的类型安全**: TypeScript全覆盖

### 风险和挑战
1. **构建复杂度**: 需要自定义webpack配置
2. **包大小**: React + Next.js可能增加扩展大小
3. **学习成本**: 团队需要熟悉React/Next.js
4. **兼容性**: 需要确保在所有Chrome版本上工作

### 成本效益分析
- **开发时间**: 4-6周完整迁移
- **维护成本**: 长期降低，代码更易维护
- **功能扩展**: 更容易添加复杂功能
- **用户体验**: 显著提升

## 🚀 替代方案: Next.js Web应用

### 架构设计
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Chrome扩展     │────│   Next.js API   │────│    数据库        │
│   (数据收集器)    │    │   (数据处理)     │    │   (数据存储)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │  Next.js 前端    │
                    │  (数据可视化)    │
                    └─────────────────┘
```

### 功能特性
- **Web仪表板**: 数据可视化、历史分析
- **API服务**: RESTful API, GraphQL
- **数据持久化**: PostgreSQL + Prisma
- **实时更新**: WebSocket连接
- **用户管理**: 多用户支持
- **云端同步**: 跨设备数据同步

### 技术栈
- **前端**: Next.js 14 + React + TypeScript
- **后端**: Next.js API Routes + Prisma
- **数据库**: PostgreSQL / MongoDB
- **部署**: Vercel / AWS
- **监控**: Sentry + Analytics

## 📊 建议选择

基于当前项目状态和需求，我推荐：

### 短期 (3个月内)
选择 **方案1: Next.js Chrome扩展重构**
- 保持现有功能完整性
- 提升开发体验
- 为未来扩展打好基础

### 长期 (6个月后)
逐步迁移到 **方案3: 混合架构**
- Chrome扩展负责数据收集
- Next.js Web应用提供高级功能
- 满足企业级需求

这样可以平滑过渡，既保持现有优势，又获得Next.js的强大功能。 