# 飞鱼FBM助手 - 项目计划

## 项目概述
飞鱼FBM助手是一个Chrome浏览器扩展，用于在飞鱼数据网站上抓取商品信息，并通过Amazon搜索检查每个商品的FBM（Fulfilled by Merchant）状态，最终导出符合条件的商品数据。

## ✅ 已完成功能

### 🔧 基础架构
- [x] Chrome扩展基础结构搭建
- [x] Manifest V3配置
- [x] Content Script注入机制
- [x] Background Service Worker设置
- [x] 权限配置（tabs, storage, scripting, downloads等）
- [x] 构建脚本（build.sh）和打包流程
- [x] 图标生成脚本（generate-icons.js）
- [x] npm包管理和依赖配置

### 🌐 网站兼容性
- [x] 支持飞鱼数据网站（feiyushuju.com）
- [x] 自动检测当前页面是否为飞鱼数据网站
- [x] 多种表格结构适配（Ant Design、标准表格等）
- [x] 多种行选择器支持
- [x] URL验证和页面状态检查

### 📊 数据抓取功能
- [x] 自动识别和抓取产品表格数据
- [x] 提取英文关键词（自动去除中文字符）
- [x] 提取中文关键词（支持正则表达式匹配）
- [x] 抓取完整产品信息（ABA趋势、排名、品牌等）
- [x] 进度条显示和状态更新
- [x] 错误处理和重试机制
- [x] 悬停操作模拟获取详细信息
- [x] 多种数据提取策略（文本内容、属性值等）

### 🛒 Amazon FBM检测
- [x] 通过新标签页访问Amazon搜索页面
- [x] 自动注入FBM检查脚本
- [x] 多种FBM检测方法：
  - [x] Prime标识检查
  - [x] "Fulfilled by Amazon"文本检查
  - [x] FBM/配送信息文本检查
  - [x] 履行类型标识检查
  - [x] 免费配送标识分析
- [x] 过滤广告和赞助产品
- [x] 检查前5个搜索结果的FBM状态
- [x] 双重筛选条件（严格/宽松）
- [x] 10秒超时机制
- [x] 自动关闭检查标签页
- [x] 强制FBM检查备用机制
- [x] 异步消息传递和结果处理

### 📈 数据分析和筛选
- [x] 严格FBM条件：前5个结果中至少3个FBM
- [x] 宽松FBM条件：前5个结果中多数FBM
- [x] FBM产品统计和比例计算
- [x] 产品分类存储（符合条件/全部产品）
- [x] 实时统计信息显示
- [x] 错误产品过滤和处理

### 💾 数据存储和管理
- [x] Chrome本地存储集成
- [x] 产品数据持久化
- [x] FBM状态信息存储
- [x] 会话间数据保持
- [x] 存储状态检查和恢复
- [x] 数据完整性验证

### 📄 数据导出功能
- [x] CSV格式导出
- [x] 两种导出模式：
  - [x] 仅FBM产品导出
  - [x] 全部产品导出（含FBM标记）
- [x] 详细数据字段包含：
  - [x] 英文/中文搜索词
  - [x] 产品属性和排名数据
  - [x] FBM统计信息
  - [x] Amazon链接
  - [x] 抓取时间戳
- [x] 自动文件命名（时间戳）
- [x] UTF-8编码支持
- [x] Service Worker兼容性（Data URL方式）
- [x] Base64编码处理

### 🎨 用户界面
- [x] 现代化弹窗界面设计
- [x] 中文本地化
- [x] 实时进度显示
- [x] 状态反馈信息
- [x] 按钮状态管理
- [x] 详细的FBM条件说明
- [x] 使用指南
- [x] 响应式进度条
- [x] 多按钮布局优化

### 🔧 技术优化
- [x] CORS跨域问题解决
- [x] XMLHttpRequest替换为标签页方案
- [x] 用户代理限制问题解决
- [x] Service Worker URL API兼容性修复
- [x] 异步消息处理
- [x] 内存泄漏防护
- [x] 标签页管理和清理机制
- [x] 请求ID追踪系统
- [x] 超时处理和重试逻辑

### 📦 开发工具和部署
- [x] 自动化构建脚本
- [x] 扩展打包流程
- [x] 图标自动生成工具
- [x] 开发环境配置
- [x] 文件复制和清理机制
- [x] 版本管理系统

## 🚧 待改进功能

### 🚨 高优先级核心功能 (v1.2)

#### 📋 智能抓取控制
- [ ] **飞鱼登录页面自动抓取**: 在飞鱼登录页面后，点击【开始抓取】自动执行选品步骤逻辑（第2-4步）
- [ ] **实时Excel写入**: 每抓到一条符合要求的链接，立即写入Excel文件中，无需等待全部完成
- [ ] **前100页默认搜索**: 完整覆盖前100页的搜索结果，确保数据完整性

#### ⏯️ 抓取状态控制
- [ ] **暂停/继续抓取功能**: 
  - [ ] 点击【暂停抓取】后，按钮文案切换为【继续抓取】
  - [ ] 点击【继续抓取】后，按钮文案切换为【暂停抓取】
  - [ ] 在完成前100页搜索之前，支持随时暂停和继续
  - [ ] 保存暂停时的进度状态，继续时从断点恢复
- [ ] **状态持久化**: 刷新页面或重启浏览器后能恢复抓取状态

#### 📥 下载结果优化
- [ ] **智能按钮状态管理**:
  - [ ] 抓取过程中：下载按钮置灰不可点击
  - [ ] 暂停抓取后：下载按钮变为可点击状态
  - [ ] 完成前100页抓取：下载按钮自动变为可点击steasteastea
- [ ] **增量下载**: 支持下载当前已抓取的部分结果

#### 📊 进度显示增强
- [ ] **详细进度统计**: 
  - [ ] 格式：`符合条件数/已搜索总数` (eg. 20/100)
  - [ ] 实时更新：每处理一条记录立即更新显示
  - [ ] 百分比进度：显示整体完成百分比
- [ ] **分阶段进度**: 显示当前处理阶段（搜索中/验证中/写入中）
- [ ] **预估剩余时间**: 根据当前速度预估完成时间

#### 📁 Excel文件处理
- [ ] **实时写入机制**: 
  - [ ] 每找到符合条件的产品立即写入Excel
  - [ ] 避免数据丢失，即使意外中断也能保留已处理数据
- [ ] **文件锁定处理**: 防止多次抓取同时写入同一文件
- [ ] **增量追加模式**: 继续抓取时向现有文件追加而非覆盖

### 🔍 检测准确性提升
- [ ] 增加更多FBM检测方法
- [ ] 机器学习模型辅助判断
- [ ] Amazon页面布局变化适配
- [ ] 多地区Amazon站点支持（.co.uk, .de等）
- [ ] 移动端Amazon页面适配
- [ ] 动态内容加载检测
- [ ] 更智能的广告过滤算法

### ⚡ 性能优化
- [ ] 并发FBM检查（控制并发数）
- [ ] 智能缓存机制（避免重复检查）
- [ ] 数据库存储支持（IndexedDB）
- [ ] 大量数据处理优化
- [ ] 内存使用优化
- [ ] 批量处理优化
- [ ] 预加载和预处理机制

### 🛡️ 错误处理和稳定性
- [ ] 网络错误重试机制
- [ ] Amazon反爬虫检测应对
- [ ] 更详细的错误日志
- [ ] 崩溃恢复机制
- [ ] 数据完整性验证
- [ ] 异常状态自动修复
- [ ] 监控和告警系统

### 📊 数据分析增强
- [ ] 历史数据对比
- [ ] FBM趋势分析
- [ ] 竞争对手分析
- [ ] 价格趋势跟踪
- [ ] 季节性分析
- [ ] 市场饱和度分析
- [ ] ROI计算和预测

### 📈 报告和可视化
- [ ] 图表展示（Chart.js集成）
- [ ] 数据统计仪表板
- [ ] PDF报告生成
- [ ] Excel格式导出
- [ ] 自定义报告模板
- [ ] 交互式数据展示
- [ ] 实时数据监控面板

### 🔄 自动化功能
- [ ] 定时自动抓取
- [ ] 数据变化监控
- [ ] 邮件通知功能
- [ ] 云端数据同步
- [ ] API接口支持
- [ ] 批量任务调度
- [ ] 智能提醒系统

### 🎯 用户体验改进
- [ ] 设置页面（检查频率、超时时间等）
- [ ] 多语言支持
- [ ] 快捷键支持
- [ ] 右键菜单集成
- [ ] 帮助文档和视频教程
- [ ] 主题切换功能
- [ ] 键盘导航支持

### 🔌 扩展功能
- [ ] 其他电商平台支持（eBay, Alibaba等）
- [ ] 更多数据源网站适配
- [ ] 竞品价格监控
- [ ] 关键词建议功能
- [ ] 社交媒体分享
- [ ] 数据导入功能
- [ ] 第三方工具集成

### 🛠️ 开发工具
- [ ] 单元测试覆盖
- [ ] 自动化测试
- [ ] 代码质量检查
- [ ] 性能监控
- [ ] 用户行为分析
- [ ] 错误追踪系统
- [ ] 持续集成/部署

### 🔒 安全和隐私
- [ ] 数据加密存储
- [ ] 用户隐私保护
- [ ] 安全传输协议
- [ ] 权限最小化原则
- [ ] 敏感信息脱敏
- [ ] 合规性检查
- [ ] 安全漏洞扫描

## 🚀 版本规划

### v1.2 (短期目标) - 高优先级
- [ ] **智能抓取控制**: 飞鱼登录页面自动抓取、实时Excel写入
- [ ] **暂停/继续抓取**: 完整的状态控制和持久化
- [ ] **智能下载管理**: 按钮状态控制、增量下载
- [ ] **进度显示增强**: 详细统计、实时更新、预估时间
- [ ] **Excel文件处理**: 实时写入、文件锁定、增量追加
- [ ] 性能优化：并发检查和缓存
- [ ] 错误处理增强

### v1.3 (中期目标)
- [ ] 用户设置页面（检查频率、超时时间等）
- [ ] 更详细的日志记录
- [ ] 多语言支持基础框架
- [ ] 数据可视化功能
- [ ] 历史数据分析
- [ ] 多站点Amazon支持
- [ ] API接口开发
- [ ] 智能推荐系统

### v2.0 (长期目标)
- [ ] 云端服务集成
- [ ] 机器学习模型
- [ ] 多平台支持
- [ ] 企业级功能
- [ ] 高级分析工具

## 📝 技术债务
- [ ] 代码重构和优化
- [ ] 更好的错误处理机制
- [ ] 统一的日志系统
- [ ] 配置文件化管理
- [ ] 单元测试补充
- [ ] 文档完善
- [ ] 代码注释标准化

## 🎯 优先级排序
1. **🚨 最高优先级**: 
   - 智能抓取控制（飞鱼登录页面自动抓取、实时Excel写入）
   - 暂停/继续抓取功能（状态切换、断点恢复）
   - 智能下载管理（按钮状态控制、增量下载）
   - 进度显示增强（详细统计、实时更新）
   - Excel文件处理（实时写入、文件锁定）

2. **高优先级**: 性能优化、错误处理、安全性
3. **中优先级**: 数据可视化、多站点支持、缓存机制、API开发
4. **低优先级**: 云端同步、机器学习、多平台扩展、高级分析

## 📋 代码质量指标
- **测试覆盖率**: 目标 80%+
- **代码质量**: ESLint 检查通过
- **性能指标**: 页面加载时间 < 3秒
- **错误率**: < 1%
- **用户满意度**: 目标 4.5+ 星

---

*最后更新: 2024年1月*  
*项目状态: 活跃开发中*  
*代码版本: v1.1*  
*优先级更新: 新增高优先级核心功能，专注智能抓取控制和用户体验优化* 