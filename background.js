let scrapedProducts = [];
let allScrapedProducts = []; // 存储所有产品，包括不符合FBM条件的
let fbmStatusInfo = {
  strictCount: 0,  // 严格FBM筛选条件的产品数
  looseCount: 0,   // 宽松FBM筛选条件的产品数
  filteredCount: 0 // 最终筛选的产品数
};

// Excel实时写入相关变量
let excelData = [];
let excelHeaders = [
  '英文搜索词', '中文搜索词', '产品名称', 'ABA趋势', 'ABA排名', 
  '热门品牌', '类别', '周变化', '点击份额', '转化份额', 
  'CPC出价数', 'FBM数量', '已检查数量', '是否足够FBM', 
  '是否大部分FBM', 'Amazon链接', '抓取时间'
];
let isExcelLocked = false;
let excelFileName = '';

// 跟踪FBM检查请求
let pendingFBMChecks = {};

// 生成时间戳
function generateTimestamp() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  
  return `${year}${month}${day}_${hours}${minutes}${seconds}`;
}

// 初始化Excel文件
function initializeExcelFile() {
  if (!excelFileName) {
    const timestamp = generateTimestamp();
    excelFileName = `飞鱼FBM产品_${timestamp}.csv`;
  }
  
  // 清空现有数据
  excelData = [];
  
  // 保存到存储
  chrome.storage.local.set({ 
    excelData: excelData,
    excelFileName: excelFileName,
    excelHeaders: excelHeaders
  });
  
  console.log(`Excel file initialized: ${excelFileName}`);
}

// 实时写入Excel数据
function writeToExcel(productData) {
  if (isExcelLocked) {
    console.log('Excel file is locked, queuing data...');
    // 可以实现队列机制，这里暂时简单等待
    setTimeout(() => writeToExcel(productData), 100);
    return;
  }
  
  isExcelLocked = true;
  
  try {
    // 将产品数据转换为CSV行
    const row = [
      productData.searchTerm || '',
      productData.searchTermCn || '',
      productData.productName || '',
      productData.abaTrend || '',
      productData.abaRanking || '',
      productData.topBrands || '',
      productData.category || '',
      productData.weeklyChange || '',
      productData.clickShare || '',
      productData.conversionShare || '',
      productData.cpcBidCount || '',
      productData.fbmCount || 0,
      productData.fbmChecked || 0,
      productData.hasEnoughFBM ? '是' : '否',
      productData.hasMostlyFBM ? '是' : '否',
      productData.amazonUrl || '',
      productData.scrapedTime || ''
    ];
    
    excelData.push(row);
    
    // 保存到本地存储
    chrome.storage.local.set({ 
      excelData: excelData,
      lastUpdate: Date.now()
    });
    
    console.log(`Added product to Excel: ${productData.searchTerm}, total rows: ${excelData.length}`);
    
  } catch (error) {
    console.error('Error writing to Excel:', error);
  } finally {
    isExcelLocked = false;
  }
}

// 导出Excel文件
function exportExcelFile() {
  try {
    if (!excelData || excelData.length === 0) {
      // 尝试从storage中获取数据
      return new Promise((resolve) => {
        chrome.storage.local.get(['excelData'], function(data) {
          if (data.excelData && data.excelData.length > 0) {
            excelData = data.excelData;
            console.log('Found excel data in storage:', excelData.length, 'rows');
            resolve(performExcelExport());
          } else {
            console.log('No excel data found');
            resolve({ success: false, message: '没有可导出的实时Excel数据' });
          }
        });
      });
    }
    
    return performExcelExport();
    
  } catch (error) {
    console.error('Error exporting Excel file:', error);
    return { success: false, message: '导出Excel文件时出错: ' + error.message };
  }
}

function performExcelExport() {
  try {
    // 创建CSV内容
    let csvContent = '\uFEFF'; // UTF-8 BOM for Excel compatibility
    
    // 添加标题行
    csvContent += excelHeaders.map(header => `"${header}"`).join(',') + '\n';
    
    // 添加数据行
    excelData.forEach(row => {
      const escapedRow = row.map(cell => {
        const cellStr = String(cell || '');
        return `"${cellStr.replace(/"/g, '""')}"`;
      });
      csvContent += escapedRow.join(',') + '\n';
    });
    
    // 将CSV内容转换为base64编码的data URL
    const base64Content = btoa(unescape(encodeURIComponent(csvContent)));
    const dataUrl = `data:text/csv;charset=utf-8;base64,${base64Content}`;
    
    chrome.downloads.download({
      url: dataUrl,
      filename: excelFileName,
      saveAs: false
    }, function(downloadId) {
      if (chrome.runtime.lastError) {
        console.error('Download failed:', chrome.runtime.lastError);
      } else {
        console.log(`Excel file downloaded: ${excelFileName}`);
      }
    });
    
    return { success: true, message: `Excel文件已导出: ${excelFileName}` };
    
  } catch (error) {
    console.error('Error in performExcelExport:', error);
    return { success: false, message: '导出Excel文件时出错: ' + error.message };
  }
}

// 取消所有进行中的FBM检查
function cancelAllFBMChecks() {
  console.log('Cancelling all pending FBM checks...');
  
  Object.keys(pendingFBMChecks).forEach(requestId => {
    const request = pendingFBMChecks[requestId];
    
    // 关闭标签页
    if (request.tabId) {
      chrome.tabs.remove(request.tabId).catch(err => console.error('Error closing tab during cancel:', err));
    }
    
    // 返回取消响应
    if (request.sendResponse) {
      request.sendResponse({
        success: false,
        message: '检查已被取消'
      });
    }
  });
  
  // 清空所有挂起的请求
  pendingFBMChecks = {};
}

// Listen for messages from content script
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
  if (request.type === 'ping') {
    // 响应连接检查
    console.log('Received ping from content script');
    sendResponse({pong: true});
    return true;
  } else if (request.type === 'storeResults') {
    console.log('Storing scraped products:', request.products.length);
    scrapedProducts = request.products;
    
    // 存储所有产品，包括不符合FBM条件的
    if (request.allProducts) {
      allScrapedProducts = request.allProducts;
      console.log('Storing all products:', allScrapedProducts.length);
    }
    
    // 存储FBM筛选信息
    fbmStatusInfo = {
      strictCount: request.strictCount || 0,
      looseCount: request.looseCount || 0,
      filteredCount: request.filteredCount || 0
    };
    
    // 存储到本地存储以便后续使用
    chrome.storage.local.set({ 
      products: scrapedProducts,
      allProducts: allScrapedProducts,
      fbmStatusInfo: fbmStatusInfo
    });
    
    return true; // 指示异步响应
  } else if (request.action === "writeToExcel") {
    // 实时写入Excel
    if (!excelFileName) {
      initializeExcelFile();
    }
    writeToExcel(request.productData);
    sendResponse({ success: true });
    return true;
  } else if (request.action === "cancelAllFBMChecks") {
    // 取消所有FBM检查 - 新增功能
    cancelAllFBMChecks();
    sendResponse({ success: true });
    return true;
  }
  return true; // 默认返回true以支持异步响应
});

// Handle export request
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
  if (request.action === "exportResults") {
    if (scrapedProducts.length === 0) {
      // 检查本地存储中是否有产品
      chrome.storage.local.get(['products', 'allProducts', 'fbmStatusInfo'], function(data) {
        if (data.products && data.products.length > 0) {
          console.log('Found products in storage:', data.products.length);
          scrapedProducts = data.products;
          
          if (data.allProducts) {
            allScrapedProducts = data.allProducts;
          }
          
          if (data.fbmStatusInfo) {
            fbmStatusInfo = data.fbmStatusInfo;
          }
          
          exportProducts(sendResponse);
        } else {
          console.log('No products found in storage');
          sendResponse({ success: false, message: '没有可导出的数据' });
        }
      });
      return true; // 指示异步响应
    } else {
      exportProducts(sendResponse);
      return true; // 指示异步响应
    }
  } else if (request.action === "exportAllResults") {
    // 导出所有产品，包括不符合FBM条件的
    if (allScrapedProducts.length === 0) {
      // 从本地存储中获取所有产品
      chrome.storage.local.get(['allProducts', 'fbmStatusInfo'], function(data) {
        if (data.allProducts && data.allProducts.length > 0) {
          console.log('Found all products in storage:', data.allProducts.length);
          allScrapedProducts = data.allProducts;
          
          if (data.fbmStatusInfo) {
            fbmStatusInfo = data.fbmStatusInfo;
          }
          
          exportAllProducts(sendResponse);
        } else {
          console.log('No all products found in storage');
          sendResponse({ success: false, message: '没有可导出的全部数据' });
        }
      });
      return true; // 指示异步响应
    } else {
      exportAllProducts(sendResponse);
      return true; // 指示异步响应
    }
  } else if (request.action === "exportExcelFile") {
    // 导出实时Excel文件
    const result = exportExcelFile();
    
    // 检查是否返回Promise
    if (result && typeof result.then === 'function') {
      // 异步处理
      result.then(response => {
        sendResponse(response);
      }).catch(error => {
        console.error('Error in exportExcelFile promise:', error);
        sendResponse({ success: false, message: '导出失败: ' + error.message });
      });
      return true; // 指示异步响应
    } else {
      // 同步处理
      sendResponse(result);
      return true;
    }
  } else if (request.action === "openAmazonTab") {
    // 打开Amazon标签页进行FBM检查
    openAmazonTab(request, sendResponse);
    return true; // 指示异步响应
  }
  return true; // 默认返回true以支持异步响应
});

// 处理打开Amazon标签页的请求
function openAmazonTab(request, sendResponse) {
  const url = request.url;
  const keyword = request.keyword;
  
  if (!url) {
    sendResponse({ 
      success: false, 
      message: '缺少URL参数'
    });
    return;
  }
  
  console.log(`Opening Amazon tab for keyword: ${keyword} at URL: ${url}`);
  
  // 创建一个唯一ID来跟踪这个请求
  const requestId = Date.now().toString() + Math.random().toString(36).substr(2, 5);
  
  // 存储回调函数以便之后使用
  pendingFBMChecks[requestId] = { 
    sendResponse: sendResponse,
    keyword: keyword,
    url: url,
    timestamp: Date.now()
  };
  
  // 打开新标签页
  chrome.tabs.create({ url: url, active: false }, function(tab) {
    console.log(`Created tab ${tab.id} for FBM check of: ${keyword}`);
    
    // 将tab ID存储在请求对象中
    pendingFBMChecks[requestId].tabId = tab.id;
    
    // 在标签页上注入FBM检查脚本
    chrome.scripting.executeScript({
      target: { tabId: tab.id },
      function: injectedFBMChecker,
      args: [requestId]
    }).then(() => {
      console.log(`Injected FBM checker script into tab ${tab.id}`);
      
      // 8秒后如果还没有收到响应，则关闭标签页并返回错误 (减少超时时间)
      setTimeout(function() {
        if (pendingFBMChecks[requestId]) {
          console.log(`FBM check timed out for ${keyword}`);
          
          // 强制进行一次FBM检查
          chrome.tabs.sendMessage(tab.id, {
            action: "forceFBMCheck",
            requestId: requestId
          }).catch(err => console.error('Error forcing FBM check:', err));
          
          // 再等待2秒，然后如果仍然没有响应，就关闭标签页
          setTimeout(function() {
            if (pendingFBMChecks[requestId]) {
              // 关闭标签页
              chrome.tabs.remove(tab.id).catch(err => console.error('Error closing tab:', err));
              
              // 返回超时错误
              const response = pendingFBMChecks[requestId].sendResponse;
              response({
                success: false,
                message: '检查FBM状态超时'
              });
              
              // 删除挂起的请求
              delete pendingFBMChecks[requestId];
            }
          }, 2000);
        }
      }, 8000); // 减少到8秒
    }).catch(err => {
      console.error('Error injecting script:', err);
      sendResponse({ 
        success: false, 
        message: '无法注入FBM检查脚本: ' + err.message
      });
      
      // 删除挂起的请求
      delete pendingFBMChecks[requestId];
      
      // 关闭标签页
      chrome.tabs.remove(tab.id).catch(err => console.error('Error closing tab:', err));
    });
  });
}

// 接收来自注入脚本的FBM检查结果
chrome.runtime.onMessage.addListener(function(message, sender, sendResponse) {
  if (message.type === 'fbmCheckResult') {
    const requestId = message.requestId;
    const results = message.results;
    
    console.log(`Received FBM check result for request ${requestId}:`, results);
    
    if (pendingFBMChecks[requestId]) {
      const { sendResponse: originalSendResponse, keyword, tabId } = pendingFBMChecks[requestId];
      
      // 关闭Amazon标签页
      if (tabId) {
        chrome.tabs.remove(tabId).catch(err => console.error('Error closing tab:', err));
      }
      
      // 发送结果回原始请求者
      originalSendResponse({
        success: true,
        fbmCount: results.fbmCount || 0,
        fbmChecked: results.totalChecked || 0,
        hasEnoughFBM: results.hasFBM || false,
        hasMostlyFBM: results.hasMostlyFBM || false,
        keyword: keyword
      });
      
      // 删除挂起的请求
      delete pendingFBMChecks[requestId];
    }
    
    return true;
  }
});

// 注入到Amazon页面的FBM检查脚本
function injectedFBMChecker(requestId) {
  console.log('FBM checker script injected, requestId:', requestId);
  
  // 用于跟踪是否已发送结果
  let resultSent = false;
  
  // 等待页面加载完成
  function waitForPageLoad() {
    return new Promise((resolve) => {
  if (document.readyState === 'complete') {
        resolve();
      } else {
        window.addEventListener('load', resolve);
    }
  });
  }
  
  // 主要的FBM检查函数
  function checkFBM() {
    if (resultSent) return; // 防止重复发送结果
    
    try {
      console.log('Starting FBM check on Amazon page');
    
      // 等待一下让页面完全加载，但减少等待时间
      setTimeout(() => {
        if (resultSent) return; // 再次检查是否已发送结果
        
        // 查找搜索结果
        const searchResults = document.querySelectorAll('.s-result-item[data-asin]:not([data-asin=""]):not(.AdHolder)');
        console.log('Found search results:', searchResults.length);
      
        // 过滤掉赞助商品
      const validResults = Array.from(searchResults).filter(result => {
          return !result.querySelector('.puis-sponsored-label-text') && 
               !result.classList.contains('AdHolder') &&
                 !result.getAttribute('data-component-type')?.includes('sp-sponsored');
      });
      
        console.log('Valid product results:', validResults.length);
        
        let fbmCount = 0;
        const resultsToCheck = Math.min(validResults.length, 5);
        
        if (resultsToCheck === 0) {
          console.log('No valid results to check');
          sendResults({
            fbmCount: 0,
            totalChecked: 0,
            hasFBM: false,
            hasMostlyFBM: false,
            error: "No valid results found"
          });
          return;
        }
        
        // 检查前5个有效搜索结果
        for (let i = 0; i < resultsToCheck; i++) {
          const result = validResults[i];
          const asin = result.getAttribute('data-asin');
      
          let isFBM = false;
        
          // 检查Prime标识
          const hasPrime = !!result.querySelector('.s-prime, .a-icon-prime');
          if (!hasPrime) {
            isFBM = true;
          }
          
          // 检查文本中的FBM指示器
          const textElements = result.querySelectorAll('.a-color-secondary, .a-size-base, .a-row');
          let hasFBMText = false;
          let hasFBAText = false;
          
          for (const el of textElements) {
            const text = el.textContent.toLowerCase();
            
            if (text.includes('ships from') || 
                text.includes('sold by') || 
                text.includes('fulfilled by merchant') ||
                text.includes('fbm') ||
                (text.includes('shipping') && !text.includes('prime'))) {
              hasFBMText = true;
            }
            
            if (text.includes('fulfilled by amazon') || 
                text.includes('fba') ||
                (text.includes('prime') && text.includes('delivery'))) {
              hasFBAText = true;
            }
          }
          
          // 综合判断
          if (hasFBMText && !hasFBAText) {
            isFBM = true;
          } else if (hasFBAText) {
            isFBM = false;
          }
          
          // 检查免费配送但非Prime
          const freeShippingText = result.textContent.includes('FREE Shipping') && !hasPrime;
          if (freeShippingText) {
            isFBM = true;
          }
          
          if (isFBM) {
            fbmCount++;
            console.log(`Result ${i+1} is FBM`);
        } else {
            console.log(`Result ${i+1} is not FBM`);
        }
      }
      
        console.log(`Found ${fbmCount} FBM products out of ${resultsToCheck} checked`);
        
        sendResults({
          fbmCount,
          totalChecked: resultsToCheck,
          hasFBM: fbmCount >= 3,
          hasMostlyFBM: fbmCount >= Math.ceil(resultsToCheck / 2)
        });
        
      }, 1500); // 减少等待时间到1.5秒
      
    } catch (error) {
      console.error('Error in FBM check:', error);
      sendResults({
          fbmCount: 0,
        totalChecked: 0,
        hasFBM: false,
          hasMostlyFBM: false,
        error: error.message
      });
    }
  }
  
  // 发送结果到background script
  function sendResults(results) {
    if (resultSent) return; // 防止重复发送
    resultSent = true;
    
    chrome.runtime.sendMessage({
      type: 'fbmCheckResult',
      requestId: requestId,
      results: results
    });
  }
  
  // 监听强制检查消息
  chrome.runtime.onMessage.addListener(function(message, sender, sendResponse) {
    if (message.action === "forceFBMCheck" && message.requestId === requestId) {
      console.log('Forcing FBM check');
      checkFBM();
    }
  });
  
  // 开始检查
  waitForPageLoad().then(() => {
    checkFBM();
  });
}

// 导出FBM产品（原有功能）
function exportProducts(sendResponse) {
  try {
    if (!scrapedProducts || scrapedProducts.length === 0) {
      sendResponse({ success: false, message: '没有找到可导出的FBM产品' });
      return;
    }

    // 生成CSV内容
    const csvContent = generateCSVContent(scrapedProducts, false);

    // 创建文件名
    const timestamp = generateTimestamp();
    const filename = `飞鱼FBM产品_${timestamp}.csv`;
    
    downloadCSVFile(csvContent, filename, sendResponse);
    
  } catch (error) {
    console.error('Error in exportProducts:', error);
    sendResponse({ success: false, message: '导出过程中发生错误: ' + error.message });
  }
}

// 导出所有产品（原有功能）
function exportAllProducts(sendResponse) {
  try {
    if (!allScrapedProducts || allScrapedProducts.length === 0) {
      sendResponse({ success: false, message: '没有找到可导出的产品数据' });
      return;
    }

    // 生成CSV内容
    const csvContent = generateCSVContent(allScrapedProducts, true);
    
    // 创建文件名
    const timestamp = generateTimestamp();
    const filename = `飞鱼全部产品_${timestamp}.csv`;
    
    downloadCSVFile(csvContent, filename, sendResponse);
    
  } catch (error) {
    console.error('Error in exportAllProducts:', error);
    sendResponse({ success: false, message: '导出过程中发生错误: ' + error.message });
  }
}

// 生成CSV内容
function generateCSVContent(products, includeAllProducts) {
  // CSV标题
    const headers = [
    '英文搜索词', '中文搜索词', '产品名称', 'ABA趋势', 'ABA排名', 
    '热门品牌', '类别', '周变化', '点击份额', '转化份额', 
    'CPC出价数', 'FBM数量', '已检查数量', '是否足够FBM', 
    '是否大部分FBM', 'Amazon链接', '抓取时间'
    ];
    
  // 创建CSV内容
  let csvContent = '\uFEFF'; // UTF-8 BOM for Excel compatibility
  
  // 添加标题行
  csvContent += headers.map(header => `"${header}"`).join(',') + '\n';
  
  // 添加数据行
  products.forEach(product => {
    const row = [
      product.searchTerm || '',
      product.searchTermCn || '',
      product.productName || '',
      product.abaTrend || '',
      product.abaRanking || '',
      product.topBrands || '',
      product.category || '',
      product.weeklyChange || '',
      product.clickShare || '',
      product.conversionShare || '',
      product.cpcBidCount || '',
      product.fbmCount || 0,
      product.fbmChecked || 0,
      product.hasEnoughFBM ? '是' : '否',
      product.hasMostlyFBM ? '是' : '否',
      product.amazonUrl || '',
      product.scrapedTime || ''
    ];

    // 转义CSV字段
    const escapedRow = row.map(field => {
      const fieldStr = String(field);
      return `"${fieldStr.replace(/"/g, '""')}"`;
    });
    
    csvContent += escapedRow.join(',') + '\n';
  });
  
  return csvContent;
}

// 下载CSV文件
function downloadCSVFile(csvContent, filename, sendResponse) {
  try {
    // 将CSV内容转换为base64编码的data URL
    const base64Content = btoa(unescape(encodeURIComponent(csvContent)));
    const dataUrl = `data:text/csv;charset=utf-8;base64,${base64Content}`;
    
    // 使用downloads API下载文件
    chrome.downloads.download({
      url: dataUrl,
      filename: filename,
      saveAs: false
    }, function(downloadId) {
      if (chrome.runtime.lastError) {
        console.error('Download failed:', chrome.runtime.lastError);
        sendResponse({ success: false, message: '下载失败: ' + chrome.runtime.lastError.message });
      } else {
        console.log('Download started, ID:', downloadId);
        sendResponse({ success: true, message: '文件下载成功' });
      }
    });
    
  } catch (error) {
    console.error('Error downloading file:', error);
    sendResponse({ success: false, message: '下载过程中发生错误: ' + error.message });
  }
}

// 清理过期的FBM检查请求
setInterval(() => {
  const now = Date.now();
  const expiredRequests = Object.keys(pendingFBMChecks).filter(
    requestId => now - pendingFBMChecks[requestId].timestamp > 20000 // 减少到20秒超时
  );
  
  expiredRequests.forEach(requestId => {
    console.log('Cleaning up expired FBM check request:', requestId);
    const request = pendingFBMChecks[requestId];
    if (request.tabId) {
      chrome.tabs.remove(request.tabId).catch(err => console.error('Error closing expired tab:', err));
    }
    delete pendingFBMChecks[requestId];
  });
}, 10000); // 每10秒检查一次

// 当扩展启动时初始化Excel文件
chrome.runtime.onStartup.addListener(() => {
  console.log('Extension started, initializing Excel file...');
  initializeExcelFile();
});

chrome.runtime.onInstalled.addListener(() => {
  console.log('Extension installed, initializing Excel file...');
  initializeExcelFile();
}); 