let products = [];
let allProducts = [];
let currentIndex = 0;
let isPaused = false;
let isRunning = false;
let forceStop = false; // 强制停止标志
let scrapingState = {
  currentPage: 0,
  totalPages: 100,
  searchedCount: 0,
  qualifiedCount: 0,
  processedRows: 0,
  totalRows: 0,
  isPageScraping: false,
  currentRowIndex: 0 // 当前页面内的产品索引
};

// 用于可中断的延迟
let delayTimeouts = [];

// 强制停止所有操作
function forceStopAllOperations() {
  console.log('🛑 FORCE STOPPING ALL OPERATIONS');
  console.log('Before force stop - isPaused:', isPaused, 'isRunning:', isRunning, 'forceStop:', forceStop);
  
  forceStop = true;
  isPaused = true;
  isRunning = false;
  clearAllDelays();
  
  console.log('After force stop - isPaused:', isPaused, 'isRunning:', isRunning, 'forceStop:', forceStop);
  
  // 取消所有进行中的FBM检查
  chrome.runtime.sendMessage({
    action: "cancelAllFBMChecks"
  }).catch(err => console.error('Error in force stop FBM cancel:', err));
}

// Helper function to safely get text content from an element
function getTextContent(element) {
  if (!element) return '';
  return element.textContent.trim();
}

// Helper function to simulate hover
async function simulateHover(element) {
  console.log('Simulating hover on element:', element);
  const mouseoverEvent = new MouseEvent('mouseover', {
    bubbles: true,
    cancelable: true,
    view: window
  });
  element.dispatchEvent(mouseoverEvent);
  await new Promise(resolve => setTimeout(resolve, 1000));
}

// 可中断的延迟函数
function interruptibleDelay(ms) {
  return new Promise((resolve, reject) => {
    if (isPaused) {
      reject(new Error('Paused'));
              return;
            }
            
    const timeoutId = setTimeout(() => {
      const index = delayTimeouts.indexOf(timeoutId);
      if (index > -1) {
        delayTimeouts.splice(index, 1);
      }
      resolve();
    }, ms);
    
    delayTimeouts.push(timeoutId);
  });
}

// 清除所有延迟
function clearAllDelays() {
  delayTimeouts.forEach(timeoutId => clearTimeout(timeoutId));
  delayTimeouts = [];
}

// 检查是否应该停止（暂停检查）
function shouldStop() {
  // 首先检查强制停止标志
  if (forceStop) {
    console.log('FORCE STOP detected');
    return true;
  }
  
  // 从存储中也检查暂停状态，确保跨标签页同步
  chrome.storage.local.get(['isPaused', 'forceStop'], function(data) {
    if (data.isPaused !== undefined) {
      isPaused = data.isPaused;
    }
    if (data.forceStop !== undefined) {
      forceStop = data.forceStop;
    }
  });
  
  const stopped = forceStop || isPaused || !isRunning;
  if (stopped) {
    console.log(`Should stop: forceStop=${forceStop}, isPaused=${isPaused}, isRunning=${isRunning}`);
  }
  return stopped;
}

// 创建Excel文件并实时写入
function createExcelWorkbook() {
  const headers = [
    '英文搜索词', '中文搜索词', '产品名称', 'ABA趋势', 'ABA排名', 
    '热门品牌', '类别', '周变化', '点击份额', '转化份额', 
    'CPC出价数', 'FBM数量', '已检查数量', '是否足够FBM', 
    '是否大部分FBM', 'Amazon链接', '抓取时间'
  ];
  
  return {
    headers: headers,
    data: []
  };
}

// 实时写入Excel数据
function writeToExcel(productInfo) {
  try {
    // 发送到background script进行Excel处理
    chrome.runtime.sendMessage({
      action: "writeToExcel",
      productData: {
        searchTerm: productInfo.searchTerm || '',
        searchTermCn: productInfo.searchTermCn || '',
        productName: productInfo.productName || '',
        abaTrend: productInfo.abaTrend || '',
        abaRanking: productInfo.abaRanking || '',
        topBrands: productInfo.topBrands || '',
        category: productInfo.category || '',
        weeklyChange: productInfo.weeklyChange || '',
        clickShare: productInfo.clickShare || '',
        conversionShare: productInfo.conversionShare || '',
        cpcBidCount: productInfo.cpcBidCount || '',
        fbmCount: productInfo.fbmCount || 0,
        fbmChecked: productInfo.fbmChecked || 0,
        hasEnoughFBM: productInfo.hasEnoughFBM || false,
        hasMostlyFBM: productInfo.hasMostlyFBM || false,
        amazonUrl: productInfo.amazonUrl || '',
        scrapedTime: productInfo.scrapedTime || ''
      }
    });
  } catch (error) {
    console.error('Error writing to Excel:', error);
  }
}

// 保存抓取状态
function saveScrapingState() {
  chrome.storage.local.set({ 
    scrapingState: scrapingState,
    products: products,
    allProducts: allProducts,
    currentIndex: currentIndex,
    isPaused: isPaused,
    isRunning: isRunning,
    forceStop: forceStop
  });
}

// 恢复抓取状态
function loadScrapingState() {
  return new Promise((resolve) => {
    chrome.storage.local.get(['scrapingState', 'products', 'allProducts', 'currentIndex', 'isPaused', 'isRunning', 'forceStop'], (data) => {
      if (data.scrapingState) {
        scrapingState = {...scrapingState, ...data.scrapingState};
      }
      if (data.products) {
        products = data.products;
      }
      if (data.allProducts) {
        allProducts = data.allProducts;
      }
      if (data.currentIndex !== undefined) {
        currentIndex = data.currentIndex;
      }
      if (data.isPaused !== undefined) {
        isPaused = data.isPaused;
      }
      if (data.isRunning !== undefined) {
        isRunning = data.isRunning;
      }
      if (data.forceStop !== undefined) {
        forceStop = data.forceStop;
      }
      resolve();
    });
  });
}

// 发送进度更新
function sendProgressUpdate(message, stage) {
  const progressMessage = {
    type: 'progress',
    progress: Math.round((scrapingState.currentPage / scrapingState.totalPages) * 100),
    currentPage: scrapingState.currentPage,
    searchedCount: scrapingState.searchedCount,
    qualifiedCount: scrapingState.qualifiedCount,
    message: message,
    stage: stage
  };
  
  sendMessageToPopup(progressMessage);
}

// 获取下一页的链接或翻页
async function navigateToNextPage() {
  try {
    // 检查是否应该停止
    if (shouldStop()) {
      console.log('Navigation cancelled - paused or stopped');
      return false;
    }
    
    // 尝试查找下一页按钮
    const nextPageSelectors = [
      '.ant-pagination-next:not(.ant-pagination-disabled)',
      '.pagination-next:not(.disabled)',
      '.next-page:not(.disabled)',
      'a[aria-label="下一页"]:not(.disabled)',
      '.page-next:not(.disabled)',
      '[data-action="next"]:not(.disabled)'
    ];
    
    let nextButton = null;
    for (const selector of nextPageSelectors) {
      nextButton = document.querySelector(selector);
      if (nextButton && !nextButton.classList.contains('disabled')) {
        break;
      }
    }
    
    if (nextButton) {
      console.log('Found next page button:', nextButton);
      nextButton.click();
      
      // 等待页面加载 - 使用可中断的延迟
      try {
        await interruptibleDelay(3000);
      } catch (error) {
        console.log('Page load wait interrupted');
        return false;
      }
      
      // 等待新数据加载
      let attempts = 0;
      while (attempts < 10) {
        if (shouldStop()) {
          console.log('New data wait cancelled - paused or stopped');
          return false;
        }
        
        const newRows = document.querySelectorAll('tbody tr, .ant-table-tbody tr');
        if (newRows.length > 0) {
          console.log(`Page ${scrapingState.currentPage + 1} loaded with ${newRows.length} rows`);
          return true;
        }
        
        try {
          await interruptibleDelay(1000);
        } catch (error) {
          console.log('New data wait interrupted');
          return false;
        }
        attempts++;
      }
      
      console.log('New page loaded successfully');
      return true;
    } else {
      console.log('No next page button found or it is disabled');
      return false;
    }
  } catch (error) {
    console.error('Error navigating to next page:', error);
    return false;
  }
}

// 检查是否在飞鱼数据网站上
function isOnFeiyuWebsite() {
  const currentUrl = window.location.href;
  const hostname = window.location.hostname;
  
  console.log(`🌐 Website check - URL: ${currentUrl}, Hostname: ${hostname}`);
  
  const isFeiyuSite = currentUrl.includes('feiyushuju.com') || hostname.includes('feiyushuju.com');
  
  console.log(`✅ Is on Feiyu website: ${isFeiyuSite}`);
  
  return isFeiyuSite;
}

// 抓取当前页面的数据
async function scrapeCurrentPage() {
  if (shouldStop()) {
    console.log('Scraping cancelled - paused or stopped');
    saveScrapingState();
    return false;
  }
  
  scrapingState.isPageScraping = true;
  sendProgressUpdate(`正在抓取第 ${scrapingState.currentPage + 1} 页...`, '页面数据获取中');
  
  try {
    // 获取表格中的所有行
    const rowSelectors = [
      'tbody tr', // 标准表格
      '.ant-table-tbody tr', // Ant Design表格
      '.table-row', // 自定义表格行
      '.data-row' // 另一种自定义表格行
    ];
    
    let rows = [];
    
    // 尝试不同的选择器来找到行
    for (const selector of rowSelectors) {
      const selectedRows = document.querySelectorAll(selector);
      if (selectedRows && selectedRows.length > 0) {
        rows = Array.from(selectedRows);
        console.log(`Found ${rows.length} rows using selector: ${selector}`);
        break;
      }
    }
    
    if (rows.length === 0) {
      console.log('No rows found on current page');
      return true; // 继续到下一页
    }
    
    scrapingState.totalRows = rows.length;
    
    // 如果是新页面，重置行索引；如果是恢复，从保存的索引开始
    if (scrapingState.currentRowIndex >= rows.length) {
      console.log(`Row index ${scrapingState.currentRowIndex} >= rows length ${rows.length}, resetting to 0`);
      scrapingState.currentRowIndex = 0; // 重置索引
    }
    
    console.log(`=== PAGE ${scrapingState.currentPage + 1} PROCESSING ===`);
    console.log(`Total rows found: ${rows.length}`);
    console.log(`Starting from row index: ${scrapingState.currentRowIndex}`);
    console.log(`Will process rows ${scrapingState.currentRowIndex} to ${rows.length - 1}`);
    
    // 从保存的行索引开始处理
    for (let i = scrapingState.currentRowIndex; i < rows.length; i++) {
      // 更新当前行索引
      scrapingState.currentRowIndex = i;
      
      console.log(`--- Processing row ${i} (${i + 1} of ${rows.length}) ---`);
      
      const stopResult = shouldStop();
      console.log(`shouldStop() returned: ${stopResult} at row ${i}`);
      
      if (stopResult) {
        console.log('🚫 Row processing cancelled - paused or stopped at row', i);
        console.log('Current state when stopped:', {
          isPaused: isPaused,
          forceStop: forceStop,
          isRunning: isRunning,
          currentRowIndex: scrapingState.currentRowIndex
        });
        saveScrapingState();
        scrapingState.isPageScraping = false;
        return false;
      }
      
      const row = rows[i];
      const cells = row.querySelectorAll('td, .ant-table-cell, .cell, .table-cell');
      
      if (!cells || cells.length < 3) {
        continue; // 跳过没有足够单元格的行
      }
      
      scrapingState.searchedCount++;
      
      // 计算已处理的行数（从开始到当前行）
      const processedRows = i + 1;
      
      sendProgressUpdate(
        `第 ${scrapingState.currentPage + 1} 页: 处理第 ${processedRows} 个产品 (共 ${scrapingState.totalRows} 个)`,
        '产品数据分析中'
      );
      
      try {
        // 提取产品信息
        const productInfo = await extractProductInfo(row, cells);
        console.log(`Extracted product info: ${productInfo?.searchTerm || 'NO_SEARCH_TERM'}`);
        
        if (productInfo && !shouldStop()) {
          allProducts.push({...productInfo});
          console.log(`Added to allProducts. Total count: ${allProducts.length}`);
          
          // 检查FBM状态 - 这里也要检查暂停状态
          try {
            console.log(`Starting FBM check for: ${productInfo.searchTerm}`);
            const fbmResult = await checkFBMStatusAsync(productInfo);
            console.log(`FBM check result:`, fbmResult);
            
            // 再次检查是否应该停止（因为FBM检查可能花费时间）
            if (shouldStop()) {
              console.log('FBM check cancelled - paused or stopped');
              saveScrapingState();
              scrapingState.isPageScraping = false;
              return false;
            }
            
            if (fbmResult.success) {
              // 更新产品的FBM信息
              productInfo.fbmCount = fbmResult.fbmCount;
              productInfo.fbmChecked = fbmResult.fbmChecked;
              productInfo.hasEnoughFBM = fbmResult.hasEnoughFBM;
              productInfo.hasMostlyFBM = fbmResult.hasMostlyFBM;
              
              // 更新allProducts中对应产品的FBM信息
              const lastIndex = allProducts.length - 1;
              if (lastIndex >= 0) {
                allProducts[lastIndex] = {...productInfo};
              }
              
              // 如果满足FBM条件，添加到产品列表并实时写入Excel
              if (fbmResult.hasEnoughFBM || fbmResult.hasMostlyFBM) {
                products.push(productInfo);
                scrapingState.qualifiedCount++;
                console.log(`✓ QUALIFIED PRODUCT: ${productInfo.searchTerm} (Total qualified: ${scrapingState.qualifiedCount})`);
                
                // 实时写入Excel
                writeToExcel(productInfo);
                
                sendProgressUpdate(
                  `找到符合条件的产品: ${productInfo.searchTerm}`,
                  '写入Excel文件中'
                );
              } else {
                console.log(`✗ NOT QUALIFIED: ${productInfo.searchTerm} (hasEnoughFBM: ${fbmResult.hasEnoughFBM}, hasMostlyFBM: ${fbmResult.hasMostlyFBM})`);
              }
            }
          } catch (fbmError) {
            if (fbmError.message === 'Paused') {
              console.log('FBM check interrupted by pause');
              saveScrapingState();
              scrapingState.isPageScraping = false;
              return false;
            }
            console.error('Error in FBM check:', fbmError);
          }
        }
        
        // 保存状态
        console.log(`Saving state after processing row ${i}. Current state:`, {
          currentPage: scrapingState.currentPage,
          currentRowIndex: scrapingState.currentRowIndex,
          searchedCount: scrapingState.searchedCount,
          qualifiedCount: scrapingState.qualifiedCount
        });
        saveScrapingState();
        
        // 添加延迟避免过快请求 - 使用可中断的延迟
        try {
          await interruptibleDelay(500);
        } catch (error) {
          if (error.message === 'Paused') {
            console.log('Processing delay interrupted by pause');
            saveScrapingState();
            scrapingState.isPageScraping = false;
            return false;
          }
        }
        
      } catch (error) {
        console.error(`Error processing row ${i}:`, error);
      }
    }
    
    // 页面处理完成，重置行索引为下一页做准备
    scrapingState.currentRowIndex = 0;
    scrapingState.isPageScraping = false;
    
    console.log(`Completed page ${scrapingState.currentPage + 1}, resetting row index for next page`);
    
    return true;
  } catch (error) {
    console.error('Error scraping current page:', error);
    scrapingState.isPageScraping = false;
    return false;
  }
}

// 提取产品信息
async function extractProductInfo(row, cells) {
  try {
    // 检查是否应该停止
    if (shouldStop()) {
      throw new Error('Paused');
    }
    
    console.log(`🔍 Extracting product info from ${cells.length} cells`);
    
    // 调试：打印所有单元格内容
    const cellContents = Array.from(cells).map((cell, index) => ({
      index,
      text: cell.textContent?.trim() || "",
      html: cell.innerHTML
    }));
    console.log('📊 Cell contents:', cellContents);
    
    // 从单元格中提取关键信息
    const keywordCell = cells[0] || {};
    const searchTerm = keywordCell.textContent?.trim() || "";
    
    console.log(`🔤 Raw search term from first cell: "${searchTerm}"`);
    
    // 提取中文关键词
    let searchTermCn = "";
    const cnKeywordElement = row.querySelector('.chinese-keyword, .cn-keyword, [data-cn-keyword]');
    if (cnKeywordElement) {
      searchTermCn = cnKeywordElement.textContent?.trim() || "";
    } else {
      const cnRegex = /[\u4e00-\u9fa5]+/g;
      
      for (const content of cellContents.map(c => c.text)) {
        const matches = content.match(cnRegex);
        if (matches && matches.length > 0) {
          searchTermCn = matches.join(" ");
          break;
        }
      }
    }
    
    console.log(`🇨🇳 Chinese search term: "${searchTermCn}"`);
    
    // 清理英文关键词
    const cleanSearchTerm = searchTerm.replace(/[\u4e00-\u9fa5]+/g, '').trim();
    
    console.log(`🔤 Clean English search term: "${cleanSearchTerm}"`);
    
    // 最终确定搜索词
    let finalSearchTerm = cleanSearchTerm || searchTerm;
    
    // 如果仍然为空，尝试其他单元格
    if (!finalSearchTerm) {
      console.log('⚠️ Primary search term empty, checking other cells...');
      for (let i = 0; i < Math.min(cells.length, 3); i++) {
        const cellText = cells[i]?.textContent?.trim() || "";
        if (cellText && cellText.length > 2) {
          finalSearchTerm = cellText.replace(/[\u4e00-\u9fa5]+/g, '').trim();
          if (finalSearchTerm) {
            console.log(`✓ Found search term in cell ${i}: "${finalSearchTerm}"`);
            break;
          }
        }
      }
    }
    
    if (!finalSearchTerm) {
      console.error('❌ No valid search term found in any cell');
      throw new Error('No valid search term found');
    }
    
    console.log(`✅ Final search term: "${finalSearchTerm}"`);
    
    const productInfo = {
      searchTerm: finalSearchTerm,
      searchTermCn: searchTermCn,
      productName: cells[1]?.textContent?.trim() || "",
      abaTrend: cells[2]?.textContent?.trim() || "",
      abaRanking: cells[3]?.textContent?.trim() || "",
      topBrands: cells[4]?.textContent?.trim() || "",
      category: cells[5]?.textContent?.trim() || "",
      weeklyChange: cells[6]?.textContent?.trim() || "",
      clickShare: cells[7]?.textContent?.trim() || "",
      conversionShare: cells[8]?.textContent?.trim() || "",
      cpcBidCount: cells[9]?.textContent?.trim() || "",
      scrapedTime: new Date().toLocaleString(),
      amazonUrl: `https://www.amazon.com/s?k=${encodeURIComponent(finalSearchTerm)}`,
      fbmCount: 0,
      fbmChecked: 0,
      hasEnoughFBM: false,
      hasMostlyFBM: false
    };
    
    console.log('📦 Product info extracted:', {
      searchTerm: productInfo.searchTerm,
      productName: productInfo.productName,
      category: productInfo.category
    });
    
    return productInfo;
  } catch (error) {
    console.error('❌ Error extracting product info:', error);
    throw error; // 重新抛出以便上层处理
  }
}

// 异步检查FBM状态
function checkFBMStatusAsync(product) {
  return new Promise((resolve, reject) => {
    // 检查是否应该停止
    if (shouldStop()) {
      reject(new Error('Paused'));
      return;
    }
    
    const searchTerm = product.searchTerm;
    console.log(`🔍 FBM Check - Received search term: "${searchTerm}"`);
    
    if (!searchTerm || searchTerm.trim().length === 0) {
      console.error('❌ FBM Check failed - No search term provided');
      resolve({
        success: false,
        message: '没有搜索词'
      });
      return;
    }
    
    // 检查搜索词是否有效（至少2个字符，不全是空格或特殊字符）
    const cleanTerm = searchTerm.trim();
    if (cleanTerm.length < 2 || !/[a-zA-Z0-9]/.test(cleanTerm)) {
      console.error(`❌ FBM Check failed - Invalid search term: "${searchTerm}"`);
      resolve({
        success: false,
        message: '搜索词无效'
      });
      return;
    }
    
    const amazonUrl = `https://www.amazon.com/s?k=${encodeURIComponent(cleanTerm)}`;
    console.log(`🛍️ Opening Amazon URL: ${amazonUrl}`);
    
    chrome.runtime.sendMessage({
      action: "openAmazonTab",
      url: amazonUrl,
      keyword: cleanTerm
    }, function(response) {
      // 再次检查是否应该停止（因为这是异步回调）
      if (shouldStop()) {
        reject(new Error('Paused'));
        return;
      }
      
      console.log(`📬 FBM Check response:`, response);
      
      if (response && response.success) {
        console.log(`✅ FBM Check successful for "${cleanTerm}"`);
        resolve({
          success: true,
          fbmCount: response.fbmCount || 0,
          fbmChecked: response.fbmChecked || 0,
          hasEnoughFBM: response.hasEnoughFBM || false,
          hasMostlyFBM: response.hasMostlyFBM || false,
          errors: response.errors || 0
        });
      } else {
        console.error(`❌ FBM Check failed for "${cleanTerm}":`, response);
        resolve({
          success: false,
          message: response?.message || 'Amazon页面检查失败'
        });
      }
    });
  });
}

// 主抓取函数 - 遍历100页
async function startScrapingProcess() {
  // 更严格的网站检查
  if (!isOnFeiyuWebsite()) {
    console.error('❌ Not on Feiyu website. Current URL:', window.location.href);
    chrome.runtime.sendMessage({
      type: 'error',
      message: '请在飞鱼数据网站 (feiyushuju.com) 的产品搜索结果页面上使用此插件'
    });
    return;
  }

  // 检查页面是否有产品数据表格
  const hasProductTable = document.querySelector('tbody tr, .ant-table-tbody tr, .table-row, .data-row');
  if (!hasProductTable) {
    console.error('❌ No product table found on current page');
    chrome.runtime.sendMessage({
      type: 'error',
      message: '当前页面没有找到产品数据表格，请确保您在产品搜索结果页面'
    });
    return;
  }

  console.log('✅ Website and page validation passed');

  // 首先检查是否已经暂停或强制停止 - 防止启动已停止的进程
  if (isPaused || forceStop) {
    console.log('Cannot start scraping - already paused or force stopped');
    return;
  }
  
  isRunning = true;
  isPaused = false;
  forceStop = false; // 清除强制停止标志
  
  // 清除所有现有的延迟
  clearAllDelays();
  
  // 状态应该已经在开始抓取时正确设置了
  // 这里不需要再次初始化
  
  console.log(`Starting scraping from page ${scrapingState.currentPage + 1}`);
  
  try {
    // 如果是从暂停状态恢复，检查当前页面状态
    if (scrapingState.currentPage > 0) {
      console.log(`Resuming from previously saved page ${scrapingState.currentPage + 1}`);
      
      // 检查当前页面的页码指示器
      const currentPageIndicator = document.querySelector('.ant-pagination-item-active, .current-page, .active-page');
      let actualCurrentPage = 1; // 默认第一页
      
      if (currentPageIndicator) {
        const pageText = currentPageIndicator.textContent.trim();
        actualCurrentPage = parseInt(pageText) || 1;
      }
      
      console.log(`Browser is actually on page ${actualCurrentPage}, saved state was page ${scrapingState.currentPage + 1}`);
      
      // 从当前浏览器页面继续，保持已有的统计数据
      scrapingState.currentPage = actualCurrentPage - 1; // 转换为0-based索引
      
      sendProgressUpdate(
        `从第 ${actualCurrentPage} 页继续抓取...`,
        '恢复抓取进度'
      );
      
      console.log(`Adjusted currentPage to ${scrapingState.currentPage} to match browser state`);
    }
    
    // 循环抓取100页 - 添加更频繁的暂停检查
    while (scrapingState.currentPage < scrapingState.totalPages) {
      // 循环开始时立即检查暂停状态
      if (shouldStop()) {
        console.log('Scraping stopped at loop start');
        saveScrapingState();
        chrome.runtime.sendMessage({ type: 'paused' });
        return;
      }
      
      sendProgressUpdate(
        `正在处理第 ${scrapingState.currentPage + 1} 页 (共 ${scrapingState.totalPages} 页)`,
        '页面导航中'
      );
      
      // 抓取当前页面前再次检查
      if (shouldStop()) {
        console.log('Scraping stopped before page scraping');
        saveScrapingState();
        chrome.runtime.sendMessage({ type: 'paused' });
        return;
      }
      
      // 抓取当前页面
      console.log(`📄 About to scrape page ${scrapingState.currentPage + 1}`);
      const pageSuccess = await scrapeCurrentPage();
      console.log(`📄 Page ${scrapingState.currentPage + 1} scraping result: ${pageSuccess}`);
      
      // 抓取后立即检查状态
      const stopAfterPage = shouldStop();
      console.log(`🔍 After page scraping - pageSuccess: ${pageSuccess}, shouldStop: ${stopAfterPage}`);
      
      if (!pageSuccess || stopAfterPage) {
        console.log('❌ Page scraping failed or stopped');
        console.log('Current state:', {
          pageSuccess: pageSuccess,
          shouldStop: stopAfterPage,
          isPaused: isPaused,
          forceStop: forceStop,
          isRunning: isRunning
        });
        
        saveScrapingState();
        
        if (isPaused) {
          console.log('📤 Sending paused message to popup');
          chrome.runtime.sendMessage({ type: 'paused' });
        }
        return;
      }
      
      scrapingState.currentPage++;
      saveScrapingState();
      
      // 页面递增后再次检查
      if (shouldStop()) {
        console.log('Scraping stopped after page increment');
        saveScrapingState();
        chrome.runtime.sendMessage({ type: 'paused' });
        return;
      }
      
      // 如果不是最后一页，导航到下一页
      if (scrapingState.currentPage < scrapingState.totalPages) {
        // 导航前检查
        if (shouldStop()) {
          console.log('Scraping stopped before navigation');
          saveScrapingState();
          chrome.runtime.sendMessage({ type: 'paused' });
          return;
        }
        
        const navigationSuccess = await navigateToNextPage();
        
        // 导航后检查
        if (!navigationSuccess || shouldStop()) {
          console.log(`Cannot navigate to page ${scrapingState.currentPage + 1} or stopped`);
          saveScrapingState();
          if (isPaused) {
            chrome.runtime.sendMessage({ type: 'paused' });
          }
          return;
        }
      }
      
      // 在页面之间添加延迟 - 使用可中断的延迟
      if (scrapingState.currentPage < scrapingState.totalPages) {
        try {
          // 延迟前检查
          if (shouldStop()) {
            console.log('Scraping stopped before inter-page delay');
            saveScrapingState();
            chrome.runtime.sendMessage({ type: 'paused' });
            return;
          }
          
          await interruptibleDelay(2000);
          
          // 延迟后检查
          if (shouldStop()) {
            console.log('Scraping stopped after inter-page delay');
            saveScrapingState();
            chrome.runtime.sendMessage({ type: 'paused' });
            return;
          }
        } catch (error) {
          if (error.message === 'Paused') {
            console.log('Inter-page delay interrupted by pause');
            saveScrapingState();
            chrome.runtime.sendMessage({ type: 'paused' });
            return;
          }
        }
      }
    }
    
    // 最终检查 - 确保在完成时没有被暂停
    if (!shouldStop()) {
      // 完成抓取
      finalizeResults();
    } else {
      saveScrapingState();
      chrome.runtime.sendMessage({ type: 'paused' });
    }
    
  } catch (error) {
    console.error('Error in scraping process:', error);
    // 错误时也要检查是否是因为暂停
    if (error.message === 'Paused') {
      saveScrapingState();
      chrome.runtime.sendMessage({ type: 'paused' });
    } else {
      chrome.runtime.sendMessage({
        type: 'error',
        message: '抓取过程中发生错误: ' + error.message
      });
    }
  }
}

// 完成抓取，汇总结果并发送
function finalizeResults() {
  isRunning = false;
  
  // 按照FBM条件筛选产品
  const strictFilteredProducts = products.filter(p => p.hasEnoughFBM);
  const looseFilteredProducts = products.filter(p => p.hasMostlyFBM);
  
  const finalProducts = strictFilteredProducts.length > 0 ? 
                      strictFilteredProducts : 
                      looseFilteredProducts;
  
  // 发送产品到后台脚本存储
  chrome.runtime.sendMessage({
    type: 'storeResults',
    products: finalProducts,
    allProducts: allProducts,
    strictCount: strictFilteredProducts.length,
    looseCount: looseFilteredProducts.length,
    filteredCount: finalProducts.length
  });
  
  // 发送完成消息
  chrome.runtime.sendMessage({
    type: 'complete',
    message: `抓取完成! 处理了 ${scrapingState.currentPage} 页，总共抓取 ${allProducts.length} 个产品，其中 ${finalProducts.length} 个符合FBM条件`
  });
  
  // 清理状态并重置抓取状态
  scrapingState = {
    currentPage: 0,
    totalPages: 100,
    searchedCount: 0,
    qualifiedCount: 0,
    processedRows: 0,
    totalRows: 0,
    isPageScraping: false
  };
  
  chrome.storage.local.remove(['scrapingState', 'currentIndex', 'isPaused', 'isRunning', 'forceStop']);
}

// 改进的消息发送函数
function sendMessageToPopup(message, retries = 3) {
  return new Promise((resolve) => {
    const attemptSend = (attempt) => {
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          console.log(`📬 Message send attempt ${attempt} failed:`, chrome.runtime.lastError.message);
          
          // 如果是因为没有接收者（popup关闭），不算作错误
          if (chrome.runtime.lastError.message.includes('Receiving end does not exist') ||
              chrome.runtime.lastError.message.includes('message channel closed')) {
            console.log('📱 Popup appears to be closed, message ignored');
            resolve(false);
            return;
          }
          
          // 其他错误，尝试重试
          if (attempt < retries) {
            setTimeout(() => attemptSend(attempt + 1), 100);
          } else {
            console.error('❌ Failed to send message after', retries, 'attempts');
            resolve(false);
          }
        } else {
          console.log('✅ Message sent successfully:', message.type);
          resolve(true);
        }
      });
    };
    
    attemptSend(1);
  });
}

// 监听来自弹出窗口的消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
  if (request.action === "startScraping") {
    console.log('Starting scraping process...');
    
    // 检查是否需要重新开始（非暂停状态的开始）
    loadScrapingState().then(() => {
      console.log('Loaded state before starting:', {
        isPaused: isPaused,
        isRunning: isRunning,
        currentPage: scrapingState.currentPage
      });
      
      // 如果不是从暂停状态恢复，则完全重置状态
      if (!isPaused) {
        console.log('Starting fresh scraping - resetting all state');
        // 完全重置所有状态
        products = [];
        allProducts = [];
        scrapingState = {
          currentPage: 0,
          totalPages: 100,
          searchedCount: 0,
          qualifiedCount: 0,
          processedRows: 0,
          totalRows: 0,
          isPageScraping: false,
          currentRowIndex: 0
        };
        isPaused = false;
        isRunning = false;
        forceStop = false;
        
        // 保存重置的状态
        saveScrapingState();
      } else {
        console.log('Resuming from paused state - keeping existing progress');
        // 从暂停状态恢复，只清除暂停标志
        isPaused = false;
        isRunning = false; // 将在startScrapingProcess中设置为true
        forceStop = false;
        
        // 保存状态
        saveScrapingState();
      }
      
      // 启动抓取
      startScrapingProcess();
    });
    sendResponse({success: true});
    return true;
  } else if (request.action === "pauseScraping") {
    console.log('🔥 RECEIVED PAUSE MESSAGE - Starting pause process...');
    console.log('Current state before pause:', {
      isPaused: isPaused,
      isRunning: isRunning,
      forceStop: forceStop,
      currentPage: scrapingState.currentPage,
      currentRowIndex: scrapingState.currentRowIndex
    });
    
    // 保存当前的产品数据到存储中，确保不丢失
    chrome.storage.local.set({
      products: products,
      allProducts: allProducts,
      currentIndex: currentIndex
    });
    
    // 使用强制停止机制确保立即停止
    forceStopAllOperations();
    
    // 立即保存暂停状态
    saveScrapingState();
    
    console.log('=== PAUSE STATE SAVED ===');
    console.log('Paused at page:', scrapingState.currentPage + 1);
    console.log('Paused at row index:', scrapingState.currentRowIndex);
    console.log('Products data:', {
      products: products.length,
      allProducts: allProducts.length,
      searchedCount: scrapingState.searchedCount,
      qualifiedCount: scrapingState.qualifiedCount
    });
    console.log('Full scraping state:', scrapingState);
    
    // 向popup确认暂停已生效
    sendMessageToPopup({ type: 'paused' }).then(success => {
      if (success) {
        sendResponse({success: true, paused: true});
      } else {
        sendResponse({success: false, message: '无法向popup发送暂停确认'});
      }
    });
    
    console.log('Pause flags set: forceStop =', forceStop, ', isPaused =', isPaused, ', isRunning =', isRunning);
    
    return true;
  } else if (request.action === "resumeScraping") {
    console.log('Resuming scraping process...');
    
    // 加载状态并恢复
    loadScrapingState().then(() => {
      console.log('=== RESUME STATE LOADED ===');
      console.log('isPaused:', isPaused);
      console.log('isRunning:', isRunning);
      console.log('currentPage:', scrapingState.currentPage);
      console.log('currentRowIndex:', scrapingState.currentRowIndex);
      console.log('searchedCount:', scrapingState.searchedCount);
      console.log('qualifiedCount:', scrapingState.qualifiedCount);
      console.log('Full loaded state:', scrapingState);
      console.log('Products arrays:', {
        products: products.length,
        allProducts: allProducts.length
      });
      
      // 只有在确实暂停时才恢复
      if (isPaused || forceStop) {
        console.log('Resuming from paused state - keeping existing progress');
        // 清除暂停标志，保持现有进度
        isPaused = false;
        isRunning = false; // 将在startScrapingProcess中设置为true
        forceStop = false;
        
        // 保存状态
        saveScrapingState();
        
        // 恢复抓取
        startScrapingProcess();
        sendResponse({success: true});
      } else {
        console.log('Cannot resume - not in paused state');
        sendResponse({success: false, message: '抓取未处于暂停状态'});
      }
    });
    return true;
  } else if (request.action === "stopScraping") {
    console.log('STOPPING scraping process completely...');
    
    // 使用强制停止机制确保立即停止
    forceStopAllOperations();
    
    // 完全重置状态
    scrapingState = {
      currentPage: 0,
      totalPages: 100,
      searchedCount: 0,
      qualifiedCount: 0,
      processedRows: 0,
      totalRows: 0,
      isPageScraping: false,
      currentRowIndex: 0
    };
    
    // 保存重置的状态
    saveScrapingState();
    
    // 向popup确认停止已生效
    sendMessageToPopup({ type: 'stopped' }).then(success => {
      if (success) {
        sendResponse({success: true, stopped: true});
      } else {
        sendResponse({success: false, message: '无法向popup发送停止确认'});
      }
    });
    
    console.log('Scraping completely stopped and reset');
    
    return true;
  } else if (request.action === "getStatus") {
    // popup询问当前状态
    console.log('Popup asking for current status:', {
      isRunning: isRunning,
      isPaused: isPaused,
      forceStop: forceStop,
      currentPage: scrapingState.currentPage
    });
    
    sendResponse({
      success: true,
      isRunning: isRunning,
      isPaused: isPaused,
      forceStop: forceStop,
      scrapingState: scrapingState
    });
    return true;
  }
});

// 在页面加载完成后自动检查状态
window.addEventListener('load', function() {
  console.log('Page loaded, checking scraping state...');
  
  if (isOnFeiyuWebsite()) {
    console.log('On Feiyu website, ready to scrape');
    loadScrapingState().then(() => {
      if (isPaused) {
        console.log('Found paused scraping state');
        chrome.runtime.sendMessage({ type: 'paused' });
      }
    });
  } else {
    console.log('Not on Feiyu website');
  }
}); 