body {
  width: 380px;
  min-height: 500px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  margin: 0;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #333;
}

h1 {
  margin: 0 0 20px 0;
  font-size: 22px;
  font-weight: 600;
  color: white;
  text-align: center;
}

.subtitle {
  font-size: 14px;
  font-weight: 400;
  opacity: 0.9;
}

.controls {
  margin-bottom: 20px;
}

button {
  width: 100%;
  padding: 12px;
  margin-bottom: 10px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

#start-btn {
  background: #4CAF50;
  color: white;
}

#start-btn:hover:not(:disabled) {
  background: #45a049;
  transform: translateY(-1px);
}

#start-btn:disabled {
  background: #cccccc;
  cursor: not-allowed;
}

#pause-btn {
  background: #ff9800;
  color: white;
  margin-bottom: 10px;
}

#pause-btn:hover {
  background: #e68900;
  transform: translateY(-1px);
}

#pause-btn.continue {
  background: #2196F3;
}

#pause-btn.continue:hover {
  background: #1976D2;
}

#stop-btn {
  background: #f44336;
  color: white;
  margin-bottom: 10px;
}

#stop-btn:hover {
  background: #d32f2f;
  transform: translateY(-1px);
}

.export-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.export-buttons button {
  flex: 1;
  margin-bottom: 0;
  font-size: 12px;
  padding: 10px;
}

#export-btn {
  background: linear-gradient(135deg, #2196F3 0%, #9C27B0 50%, #FF5722 100%);
  color: white;
  font-weight: 600;
  font-size: 14px;
  padding: 12px;
}

#export-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #1976D2 0%, #7B1FA2 50%, #E64A19 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

#export-btn:disabled {
  background: #cccccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.progress-container {
  background: white;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.progress-bar {
  width: 0%;
  height: 8px;
  background: linear-gradient(90deg, #4CAF50, #45a049);
  border-radius: 4px;
  transition: width 0.3s ease;
  margin-bottom: 10px;
}

.progress-text {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.status-section {
  background: white;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.status {
  font-size: 14px;
  text-align: center;
  color: #333;
  margin-bottom: 8px;
}

.stage-info {
  font-size: 12px;
  color: #666;
  text-align: center;
  margin-bottom: 5px;
  font-style: italic;
}

.time-estimate {
  font-size: 12px;
  color: #666;
  text-align: center;
  font-weight: 500;
}

.stats-container {
  background: white;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.stats-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.stats-row:last-child {
  margin-bottom: 0;
}

.stats-label {
  color: #666;
  font-weight: 500;
}

#searched-count, #qualified-count, #current-page {
  color: #333;
  font-weight: 600;
}

.info-text {
  background: white;
  border-radius: 8px;
  padding: 15px;
  font-size: 12px;
  line-height: 1.5;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.info-text h2 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
}

.info-text p {
  margin: 8px 0;
  color: #555;
}

.info-text ul {
  margin: 8px 0;
  padding-left: 18px;
  color: #555;
}

.info-text li {
  margin-bottom: 4px;
}

.info-text strong {
  color: #333;
} 