// 调试脚本 - 用于诊断扩展连接问题和网站数据
console.log('Debug script loaded');
console.log('Current URL:', window.location.href);
console.log('Extension available:', typeof chrome !== 'undefined');
console.log('Runtime available:', typeof chrome.runtime !== 'undefined');

// 网站数据分析函数
function analyzeWebsiteData() {
  console.log('\n=== 网站数据分析 ===');
  
  // 1. 查找表格
  const tables = document.querySelectorAll('table');
  console.log('表格数量:', tables.length);
  
  // 2. 查找数据行
  const rowSelectors = [
    'tbody tr',
    '.ant-table-tbody tr', 
    '.table-row',
    '.data-row',
    'tr'
  ];
  
  let totalRows = 0;
  rowSelectors.forEach(selector => {
    const rows = document.querySelectorAll(selector);
    if (rows.length > 0) {
      console.log(`选择器 "${selector}": ${rows.length} 行`);
      totalRows = Math.max(totalRows, rows.length);
    }
  });
  
  // 3. 检查无数据状态
  const pageText = document.body.textContent;
  const noDataIndicators = ['暂无数据', '共0个结果', 'No data', 'No results'];
  const hasNoData = noDataIndicators.some(indicator => pageText.includes(indicator));
  console.log('无数据状态:', hasNoData);
  
  // 4. 检查页面内容
  console.log('页面总字符数:', pageText.length);
  console.log('页面标题:', document.title);
  
  return {
    tableCount: tables.length,
    rowCount: totalRows,
    hasNoData: hasNoData,
    pageLength: pageText.length
  };
}

// 检查扩展连接
function debugExtensionConnection() {
  console.log('=== Extension Connection Debug ===');
  
  if (typeof chrome === 'undefined') {
    console.error('Chrome extension API not available');
    return;
  }
  
  if (typeof chrome.runtime === 'undefined') {
    console.error('Chrome runtime not available');
    return;
  }
  
  // 尝试ping background script
  try {
    chrome.runtime.sendMessage({type: 'debug_ping'}, function(response) {
      if (chrome.runtime.lastError) {
        console.error('Background script ping failed:', chrome.runtime.lastError);
      } else {
        console.log('Background script ping successful:', response);
      }
    });
  } catch (error) {
    console.error('Error sending message to background:', error);
  }
  
  // 检查manifest信息
  try {
    const manifest = chrome.runtime.getManifest();
    console.log('Extension manifest:', manifest);
    console.log('Content scripts:', manifest.content_scripts);
  } catch (error) {
    console.error('Error getting manifest:', error);
  }
  
  // 检查页面是否匹配content script规则
  const currentUrl = window.location.href;
  console.log('Current page URL:', currentUrl);
  
  const feiyuPatterns = [
    'feiyushuju.com',
    'www.feiyushuju.com'
  ];
  
  const matches = feiyuPatterns.some(pattern => currentUrl.includes(pattern));
  console.log('URL matches Feiyu patterns:', matches);
  
  // 检查是否有其他扩展冲突
  console.log('User agent:', navigator.userAgent);
  console.log('Document ready state:', document.readyState);
}

// 立即运行诊断
debugExtensionConnection();

// 页面加载完成后再次运行
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', debugExtensionConnection);
} else {
  setTimeout(debugExtensionConnection, 1000);
}

// 监听来自popup的调试消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
  if (request.type === 'debug_test') {
    console.log('Received debug test message from popup');
    sendResponse({
      success: true,
      url: window.location.href,
      readyState: document.readyState,
      timestamp: new Date().toISOString()
    });
    return true;
  }
  
  if (request.type === 'ping') {
    console.log('Received ping from popup');
    sendResponse({pong: true});
    return true;
  }
});

console.log('Debug script setup complete'); 