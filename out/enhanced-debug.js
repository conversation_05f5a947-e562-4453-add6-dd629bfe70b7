// 增强版调试脚本 - 专门用于分析飞鱼数据网站
console.log('=== 飞鱼数据网站增强调试分析 ===');

function performDetailedAnalysis() {
  const results = {
    url: window.location.href,
    title: document.title,
    timestamp: new Date().toISOString()
  };
  
  // 1. 页面基本信息
  console.log('📄 页面信息:');
  console.log('  URL:', results.url);
  console.log('  标题:', results.title);
  console.log('  加载状态:', document.readyState);
  
  // 2. 查找所有表格
  console.log('\n📊 表格分析:');
  const allTables = document.querySelectorAll('table');
  console.log(`  找到 ${allTables.length} 个表格`);
  
  results.tables = [];
  allTables.forEach((table, index) => {
    const tableInfo = {
      index: index + 1,
      className: table.className,
      id: table.id,
      rows: table.querySelectorAll('tr').length,
      bodyRows: table.querySelectorAll('tbody tr').length,
      headerCells: table.querySelectorAll('th').length,
      dataCells: table.querySelectorAll('td').length
    };
    
    results.tables.push(tableInfo);
    console.log(`  表格 ${index + 1}:`, tableInfo);
    
    // 分析表格内容
    const rows = table.querySelectorAll('tr');
    if (rows.length > 0) {
      console.log(`    第一行内容: "${rows[0].textContent.trim().substring(0, 80)}..."`);
      
      const bodyRows = table.querySelectorAll('tbody tr');
      if (bodyRows.length > 0) {
        console.log(`    第一个数据行: "${bodyRows[0].textContent.trim().substring(0, 80)}..."`);
      }
    }
  });
  
  // 3. 使用扩展的选择器进行测试
  console.log('\n🔍 扩展选择器测试:');
  const selectors = [
    'tbody tr',
    '.ant-table-tbody tr',
    '.table-row', 
    '.data-row',
    'tr[data-row-key]',
    '.vxe-body--row',
    '.el-table__row'
  ];
  
  results.selectorResults = {};
  selectors.forEach(selector => {
    const elements = document.querySelectorAll(selector);
    results.selectorResults[selector] = elements.length;
    
    console.log(`  "${selector}": ${elements.length} 个元素`);
    
    if (elements.length > 0) {
      // 分析第一个元素
      const firstElement = elements[0];
      const cells = firstElement.querySelectorAll('td, .ant-table-cell, .cell, .table-cell, .vxe-body--column');
      console.log(`    第一个元素有 ${cells.length} 个单元格`);
      
      if (cells.length > 0) {
        console.log(`    第一个单元格: "${cells[0].textContent.trim().substring(0, 50)}..."`);
        console.log(`    第一个单元格类名: "${cells[0].className}"`);
      }
    }
  });
  
  // 4. 检查数据状态
  console.log('\n📋 数据状态检查:');
  const pageText = document.body.textContent;
  const noDataIndicators = [
    '暂无数据',
    '共0个结果', 
    '当前已加载0个',
    'No data',
    'No results',
    '没有找到数据',
    '无数据',
    'Empty'
  ];
  
  results.dataStatus = {
    hasNoDataIndicator: false,
    foundIndicators: []
  };
  
  noDataIndicators.forEach(indicator => {
    if (pageText.includes(indicator)) {
      results.dataStatus.hasNoDataIndicator = true;
      results.dataStatus.foundIndicators.push(indicator);
      console.log(`  ✓ 发现: "${indicator}"`);
    }
  });
  
  if (!results.dataStatus.hasNoDataIndicator) {
    console.log('  ✗ 未发现明显的无数据提示');
  }
  
  // 5. 查找搜索相关元素
  console.log('\n🔎 搜索功能检查:');
  const searchInputs = document.querySelectorAll('input[type="text"], input[placeholder*="搜索"], input[placeholder*="关键"], input[placeholder*="keyword"], .ant-input');
  const searchButtons = document.querySelectorAll('button[class*="search"], button[class*="query"], .ant-btn');
  
  results.searchElements = {
    inputs: searchInputs.length,
    buttons: searchButtons.length
  };
  
  console.log(`  输入框: ${searchInputs.length} 个`);
  console.log(`  按钮: ${searchButtons.length} 个`);
  
  // 分析输入框
  Array.from(searchInputs).slice(0, 3).forEach((input, index) => {
    console.log(`  输入框 ${index + 1}:`, {
      placeholder: input.placeholder,
      value: input.value,
      id: input.id,
      className: input.className
    });
  });
  
  // 6. 检查页面动态内容
  console.log('\n⚡ 动态内容检查:');
  const loadingElements = document.querySelectorAll('.loading, .spinner, .ant-spin, .el-loading-mask');
  console.log(`  加载指示器: ${loadingElements.length} 个`);
  
  // 检查是否有异步渲染的迹象
  const frameworks = [];
  if (window.Vue) frameworks.push('Vue.js');
  if (window.React) frameworks.push('React');
  if (window.Angular) frameworks.push('Angular');
  if (window.__NUXT__) frameworks.push('Nuxt.js');
  if (document.querySelector('[data-reactroot]')) frameworks.push('React (detected)');
  if (document.querySelector('.ant-')) frameworks.push('Ant Design');
  if (document.querySelector('.el-')) frameworks.push('Element UI');
  
  results.frameworks = frameworks;
  console.log('  检测到的框架:', frameworks.length > 0 ? frameworks.join(', ') : '无');
  
  // 7. 生成建议
  console.log('\n💡 分析建议:');
  
  if (results.dataStatus.hasNoDataIndicator) {
    console.log('  ⚠️  页面显示无数据，需要先进行搜索');
    console.log('  👆 建议: 在搜索框输入关键词并点击搜索按钮');
  }
  
  if (results.tables.length === 0) {
    console.log('  ⚠️  未找到表格元素');
    console.log('  👆 建议: 检查页面是否完全加载，或尝试在有数据的页面使用');
  }
  
  const hasValidRows = Object.values(results.selectorResults).some(count => count > 0);
  if (!hasValidRows) {
    console.log('  ⚠️  所有选择器都未找到数据行');
    console.log('  👆 建议: 页面可能使用了不同的结构，需要更新选择器');
  }
  
  // 8. 输出完整结果对象
  console.log('\n📊 完整分析结果:');
  console.log(results);
  
  return results;
}

// 执行分析
const analysisResults = performDetailedAnalysis();

// 将结果存储到window对象供其他脚本访问
window.debugAnalysis = analysisResults;

console.log('\n✅ 调试分析完成! 结果已保存到 window.debugAnalysis');
console.log('💡 可以在控制台输入 window.debugAnalysis 查看完整结果'); 