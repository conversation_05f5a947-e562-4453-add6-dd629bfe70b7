document.addEventListener('DOMContentLoaded', function() {
  const startBtn = document.getElementById('start-btn');
  const pauseBtn = document.getElementById('pause-btn');
  const stopBtn = document.getElementById('stop-btn');
  const exportBtn = document.getElementById('export-btn');
  const statusElem = document.getElementById('status');
  const progressElement = document.getElementById('progress');
  const progressPercentage = document.getElementById('progress-percentage');
  const progressDetails = document.getElementById('progress-details');
  const stageInfo = document.getElementById('stage-info');
  const timeEstimate = document.getElementById('time-estimate');
  const statsContainer = document.querySelector('.stats-container');
  const searchedCount = document.getElementById('searched-count');
  const qualifiedCount = document.getElementById('qualified-count');
  const currentPage = document.getElementById('current-page');

  let scrapingState = {
    isRunning: false,
    isPaused: false,
    currentPage: 0,
    totalPages: 100,
    searchedCount: 0,
    qualifiedCount: 0,
    startTime: null,
    lastUpdateTime: null
  };

  // 检查是否有已经抓取的产品数据和状态
  chrome.storage.local.get(['products', 'allProducts', 'fbmStatusInfo', 'scrapingState', 'excelData'], function(data) {
    if (data.scrapingState) {
      scrapingState = { ...scrapingState, ...data.scrapingState };
      
      // 如果不是正在运行或暂停状态，确保状态正确重置
      if (!scrapingState.isRunning && !scrapingState.isPaused) {
        scrapingState.currentPage = 0;
        scrapingState.searchedCount = 0;
        scrapingState.qualifiedCount = 0;
        scrapingState.startTime = null;
        scrapingState.lastUpdateTime = null;
        saveState(); // 保存重置后的状态
      }
      
      updateUI();
    }

    // 检查是否有数据可供导出（合并所有类型的数据）
    if ((data.products && data.products.length > 0) || 
        (data.allProducts && data.allProducts.length > 0) || 
        (data.excelData && data.excelData.length > 0)) {
      exportBtn.disabled = false;
      
      // 显示数据信息
      if (data.fbmStatusInfo) {
        const strictCount = data.fbmStatusInfo.strictCount || 0;
        const looseCount = data.fbmStatusInfo.looseCount || 0;
        const filteredCount = data.fbmStatusInfo.filteredCount || 0;
        
        statusElem.textContent = `已找到 ${filteredCount} 个FBM产品可供导出。`;
        statusElem.textContent += ` 符合严格条件的有 ${strictCount} 个，符合宽松条件的有 ${looseCount} 个。`;
      } else if (data.products && data.products.length > 0) {
        statusElem.textContent = `已找到 ${data.products.length} 个产品可供导出。`;
      } else if (data.allProducts && data.allProducts.length > 0) {
        statusElem.textContent = `已找到 ${data.allProducts.length} 个产品可供导出。`;
      } else if (data.excelData && data.excelData.length > 0) {
        statusElem.textContent = `已找到 ${data.excelData.length} 个产品可供导出。`;
      }
    }
  });

  // 检查当前页面是否是飞鱼数据网站并同步状态
  chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
    const currentUrl = tabs[0].url;
    if (!currentUrl.includes('feiyushuju.com')) {
      startBtn.disabled = true;
      statusElem.textContent = '请在飞鱼数据网站使用此插件';
      return;
    } else {
      startBtn.disabled = false;
      
      // 主动询问content script的真实状态
      chrome.tabs.sendMessage(tabs[0].id, {action: "getStatus"}, function(response) {
        if (chrome.runtime.lastError) {
          console.log('无法获取content script状态:', chrome.runtime.lastError);
          if (!scrapingState.isRunning && !scrapingState.isPaused) {
            statusElem.textContent = '准备就绪，点击开始抓取';
          }
        } else if (response && response.success) {
          console.log('获取到content script状态:', response);
          
          // 使用content script的真实状态更新popup状态
          if (response.isRunning) {
            scrapingState.isRunning = true;
            scrapingState.isPaused = false;
            if (response.scrapingState) {
              scrapingState = { ...scrapingState, ...response.scrapingState };
            }
            statusElem.textContent = '抓取进行中...';
            updateUI();
            saveState();
          } else if (response.isPaused) {
            scrapingState.isRunning = false;
            scrapingState.isPaused = true;
            if (response.scrapingState) {
              scrapingState = { ...scrapingState, ...response.scrapingState };
            }
            statusElem.textContent = '抓取已暂停，可点击继续抓取';
            updateUI();
            saveState();
          } else {
            // 不在运行状态
            if (!scrapingState.isRunning && !scrapingState.isPaused) {
              statusElem.textContent = '准备就绪，点击开始抓取';
            }
          }
        }
      });
    }
  });

  function updateUI() {
    // 更新进度条
    const progress = Math.round((scrapingState.currentPage / scrapingState.totalPages) * 100);
    progressElement.style.width = progress + '%';
    progressPercentage.textContent = progress + '%';
    progressDetails.textContent = `${scrapingState.qualifiedCount}/${scrapingState.searchedCount}`;

    // 更新统计信息
    searchedCount.textContent = scrapingState.searchedCount;
    qualifiedCount.textContent = scrapingState.qualifiedCount;
    currentPage.textContent = `${scrapingState.currentPage}/${scrapingState.totalPages}`;

    // 显示或隐藏元素
    if (scrapingState.isRunning || scrapingState.isPaused) {
      statsContainer.style.display = 'block';
      stageInfo.style.display = 'block';
      timeEstimate.style.display = 'block';
    }

    // 更新按钮状态
    if (scrapingState.isRunning) {
      startBtn.style.display = 'none';
      pauseBtn.style.display = 'block';
      stopBtn.style.display = 'block';
      pauseBtn.textContent = '暂停抓取';
      pauseBtn.classList.remove('continue');
      exportBtn.disabled = true;
    } else if (scrapingState.isPaused) {
      startBtn.style.display = 'none';
      pauseBtn.style.display = 'block';
      stopBtn.style.display = 'block';
      pauseBtn.textContent = '继续抓取';
      pauseBtn.classList.add('continue');
      exportBtn.disabled = false;
    } else {
      startBtn.style.display = 'block';
      pauseBtn.style.display = 'none';
      stopBtn.style.display = 'block'; // 停止按钮默认显示
      // 根据是否有数据决定导出按钮状态
    }

    // 更新预估时间
    if (scrapingState.isRunning && scrapingState.startTime && scrapingState.currentPage > 0) {
      const elapsed = Date.now() - scrapingState.startTime;
      const avgTimePerPage = elapsed / scrapingState.currentPage;
      const remainingPages = scrapingState.totalPages - scrapingState.currentPage;
      const estimatedTime = Math.round((remainingPages * avgTimePerPage) / 1000 / 60);
      timeEstimate.textContent = `预估剩余时间: ${estimatedTime} 分钟`;
    }
  }

  function saveState() {
    chrome.storage.local.set({ scrapingState: scrapingState });
  }

  startBtn.addEventListener('click', function() {
    scrapingState.isRunning = true;
    scrapingState.isPaused = false;
    scrapingState.startTime = Date.now();
    scrapingState.lastUpdateTime = Date.now();
    
    updateUI();
    saveState();
    
    statusElem.textContent = '开始抓取过程...';
    stageInfo.textContent = '正在初始化...';
    
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      try {
        chrome.tabs.sendMessage(tabs[0].id, {action: "startScraping"}, function(response) {
          if (chrome.runtime.lastError) {
            console.error('Error starting scraping:', chrome.runtime.lastError);
            statusElem.textContent = '启动抓取失败: ' + chrome.runtime.lastError.message;
            scrapingState.isRunning = false;
            updateUI();
            saveState();
          }
        });
      } catch (error) {
        console.error('Error sending message:', error);
        statusElem.textContent = '发生错误: ' + error.message;
        scrapingState.isRunning = false;
        updateUI();
        saveState();
      }
    });
  });

  pauseBtn.addEventListener('click', function() {
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      if (scrapingState.isRunning) {
        // 暂停抓取
        scrapingState.isRunning = false;
        scrapingState.isPaused = true;
        statusElem.textContent = '抓取已暂停';
        stageInfo.textContent = '点击继续抓取以恢复';
        
        chrome.tabs.sendMessage(tabs[0].id, {action: "pauseScraping"});
      } else if (scrapingState.isPaused) {
        // 继续抓取
        scrapingState.isRunning = true;
        scrapingState.isPaused = false;
        statusElem.textContent = '继续抓取...';
        stageInfo.textContent = '正在恢复抓取进度...';
        
        chrome.tabs.sendMessage(tabs[0].id, {action: "resumeScraping"});
      }
      
      updateUI();
      saveState();
    });
  });

  stopBtn.addEventListener('click', function() {
    // 如果当前正在抓取，则确认停止操作
    if (scrapingState.isRunning || scrapingState.isPaused) {
      if (!confirm('确定要停止抓取吗？停止后将清除当前进度并重置所有状态。')) {
        return;
      }
    }
    
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      // 停止抓取并重置所有状态
      scrapingState.isRunning = false;
      scrapingState.isPaused = false;
      
      statusElem.textContent = '已重置所有状态';
      stageInfo.textContent = '';
      timeEstimate.textContent = '';
      
      // 发送停止消息到content script
      chrome.tabs.sendMessage(tabs[0].id, {action: "stopScraping"}, function(response) {
        if (chrome.runtime.lastError) {
          console.error('Error stopping scraping:', chrome.runtime.lastError);
        }
      });
      
      // 完全重置所有状态和数据
      const resetState = {
        isRunning: false,
        isPaused: false,
        currentPage: 0,
        totalPages: 100,
        searchedCount: 0,
        qualifiedCount: 0,
        startTime: null,
        lastUpdateTime: null
      };
      
      scrapingState = resetState;
      
      // 清除所有存储的数据
      chrome.storage.local.clear(function() {
        chrome.storage.local.set({ scrapingState: resetState }, function() {
          updateUI();
          exportBtn.disabled = true;
          statusElem.textContent = '所有状态已重置，可重新开始抓取';
          
          // 隐藏统计容器
          statsContainer.style.display = 'none';
          stageInfo.style.display = 'none';
          timeEstimate.style.display = 'none';
        });
      });
    });
  });

  exportBtn.addEventListener('click', function() {
    statusElem.textContent = '正在导出数据...';
    exportBtn.disabled = true;
    
    // 检查可用的数据类型，优先导出实时Excel数据
    chrome.storage.local.get(['excelData', 'products', 'allProducts'], function(data) {
      let exportAction = null;
      let exportType = '';
      
      // 优先级：实时Excel > FBM产品 > 全部产品
      if (data.excelData && data.excelData.length > 0) {
        exportAction = "exportExcelFile";
        exportType = '实时Excel';
      } else if (data.products && data.products.length > 0) {
        exportAction = "exportResults";
        exportType = 'FBM产品';
      } else if (data.allProducts && data.allProducts.length > 0) {
        exportAction = "exportAllResults";
        exportType = '全部产品';
      }
      
      if (exportAction) {
        statusElem.textContent = `导出${exportType}中...`;
        
        chrome.runtime.sendMessage({action: exportAction}, function(response) {
          exportBtn.disabled = false;
          
          if (chrome.runtime.lastError) {
            console.error('Error exporting:', chrome.runtime.lastError);
            statusElem.textContent = `导出${exportType}失败: ` + chrome.runtime.lastError.message;
            return;
          }
          
          if (response && response.success) {
            statusElem.textContent = `导出${exportType}成功!`;
          } else {
            statusElem.textContent = `导出${exportType}失败: ` + (response ? response.message : '未知错误');
          }
        });
      } else {
        exportBtn.disabled = false;
        statusElem.textContent = '没有可导出的数据';
      }
    });
  });

  // Listen for progress updates from content script
  chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    if (request.type === 'progress') {
      scrapingState.currentPage = request.currentPage || scrapingState.currentPage;
      scrapingState.searchedCount = request.searchedCount || scrapingState.searchedCount;
      scrapingState.qualifiedCount = request.qualifiedCount || scrapingState.qualifiedCount;
      scrapingState.lastUpdateTime = Date.now();
      
      updateUI();
      saveState();
      
      statusElem.textContent = request.message;
      if (request.stage) {
        stageInfo.textContent = request.stage;
      }
      
      // 如果有新的符合条件的产品，启用导出按钮
      if (scrapingState.qualifiedCount > 0) {
        exportBtn.disabled = false;
      }
      
      sendResponse({success: true}); // 确保发送响应
    } else if (request.type === 'complete') {
      // 完全重置抓取状态
      scrapingState.isRunning = false;
      scrapingState.isPaused = false;
      scrapingState.currentPage = 0; // 重置为0，为下次抓取准备
      scrapingState.searchedCount = 0;
      scrapingState.qualifiedCount = 0;
      scrapingState.startTime = null;
      scrapingState.lastUpdateTime = null;
      
      updateUI();
      saveState();
      
      // 隐藏统计容器，因为抓取已完成
      statsContainer.style.display = 'none';
      stageInfo.style.display = 'none';
      timeEstimate.style.display = 'none';
      
      chrome.storage.local.get(['products', 'allProducts', 'filteredCount'], function(data) {
        const totalCount = data.allProducts ? data.allProducts.length : 0;
        const filteredCount = data.filteredCount !== undefined ? data.filteredCount : 
                             (data.products ? data.products.length : 0);
        
        if (data.products && data.products.length > 0) {
          exportBtn.disabled = false;
          statusElem.textContent = `抓取完成! 共获取 ${totalCount} 个产品，其中 ${filteredCount} 个满足FBM条件`;
        } else if (totalCount > 0) {
          exportBtn.disabled = false;
          statusElem.textContent = `抓取完成，获取 ${totalCount} 个产品，但没有产品满足FBM条件`;
        } else {
          statusElem.textContent = '抓取完成，没有找到任何产品';
        }
        
        stageInfo.textContent = '所有页面处理完成';
        timeEstimate.textContent = '';
        
        sendResponse({success: true}); // 确保发送响应
      });
    } else if (request.type === 'error') {
      scrapingState.isRunning = false;
      updateUI();
      saveState();
      
      statusElem.textContent = request.message || '抓取过程中发生错误';
      stageInfo.textContent = '抓取已停止';
      
      sendResponse({success: true}); // 确保发送响应
    } else if (request.type === 'paused') {
      scrapingState.isRunning = false;
      scrapingState.isPaused = true;
      updateUI();
      saveState();
      
      statusElem.textContent = '抓取已暂停，可点击继续抓取';
      stageInfo.textContent = '等待用户操作...';
      exportBtn.disabled = false; // 暂停时可以导出数据
      
      sendResponse({success: true}); // 确保发送响应
    } else if (request.type === 'stopped') {
      scrapingState.isRunning = false;
      scrapingState.isPaused = false;
      scrapingState.currentPage = 0; // 重置页面
      updateUI();
      saveState();
      
      statusElem.textContent = '抓取已完全停止，进度已重置';
      stageInfo.textContent = '可重新开始抓取或导出已有数据';
      timeEstimate.textContent = '';
      
      // 检查是否有数据可导出
      chrome.storage.local.get(['products', 'allProducts', 'excelData'], function(data) {
        if ((data.products && data.products.length > 0) || 
            (data.allProducts && data.allProducts.length > 0) || 
            (data.excelData && data.excelData.length > 0)) {
          exportBtn.disabled = false;
        }
        
        sendResponse({success: true}); // 确保发送响应
      });
    } else {
      // 对于未知消息类型，也要发送响应
      sendResponse({success: false, message: 'Unknown message type'});
    }
    
    return true; // 支持异步响应
  });
}); 