# 飞鱼FBM助手 Chrome扩展 v1.2

这是一个Chrome浏览器扩展，帮助您在飞鱼数据网站上自动筛选FBM（Fulfilled by Merchant）产品。

## ✨ v1.2 新功能特性

### 🚀 智能抓取控制
- **100页自动抓取**: 系统自动遍历前100页数据，确保数据完整性
- **实时Excel写入**: 每找到符合条件的产品立即写入Excel文件
- **自动页面导航**: 智能识别和点击下一页按钮

### ⏯️ 抓取状态控制
- **暂停/继续抓取**: 随时暂停抓取过程，支持从断点恢复
- **状态持久化**: 刷新页面或重启浏览器后能恢复抓取状态
- **智能按钮管理**: 根据抓取状态动态控制按钮可用性

### 📊 进度显示增强
- **详细进度统计**: 显示格式 `符合条件数/已搜索总数` (例如: 20/100)
- **实时更新**: 每处理一条记录立即更新显示
- **预估剩余时间**: 基于当前处理速度智能预估完成时间
- **分阶段进度**: 显示当前处理阶段（搜索中/验证中/写入中）

### 📁 Excel文件处理
- **实时写入机制**: 避免数据丢失，即使意外中断也保留已处理数据
- **文件锁定处理**: 防止多次抓取同时写入同一文件
- **增量追加模式**: 继续抓取时向现有文件追加而非覆盖

### 📥 导出选项
- **导出实时Excel**: 下载当前已抓取的符合条件产品
- **导出FBM产品**: 仅导出满足FBM条件的产品
- **导出全部产品**: 导出所有抓取的产品（含FBM标记）

## 功能特点

- 自动抓取飞鱼数据网站的产品信息
- 通过Amazon搜索验证FBM产品状态
- 支持多种筛选条件（严格/宽松）
- 实时Excel文件写入
- 完整的状态管理和恢复
- 现代化用户界面

## 安装方法

1. 克隆或下载此仓库
2. 打开Chrome浏览器，访问 `chrome://extensions/`
3. 在右上角启用"开发者模式"
4. 点击"加载已解压的扩展程序"并选择扩展目录
5. 扩展将出现在Chrome工具栏中

## 使用方法

1. 访问 https://www.feiyushuju.com/home
2. 点击Chrome工具栏中的扩展图标
3. 点击"开始抓取"按钮
4. 系统将自动抓取前100页数据
5. 可随时点击"暂停抓取"/"继续抓取"控制进程
6. 使用对应的导出按钮下载结果

## 数据收集

扩展会收集以下产品信息：
- 英文/中文搜索词
- 产品名称和详细信息
- ABA趋势和排名数据
- FBM卖家数量统计
- Amazon产品链接
- 抓取时间戳
- FBM验证状态

## 筛选条件

### 严格条件
- 前5个搜索结果中至少有3个是FBM产品

### 宽松条件  
- 前5个搜索结果中多数是FBM产品

## 系统要求

- Chrome浏览器 88+
- 需要访问飞鱼数据网站和Amazon的权限
- 推荐稳定的网络连接

## 技术特性

- **Manifest V3**: 使用最新的Chrome扩展规范
- **Service Worker**: 高效的后台处理
- **智能错误处理**: 完善的重试和恢复机制
- **内存优化**: 防止内存泄漏和资源浪费
- **异步处理**: 非阻塞的数据处理流程

## 注意事项

- 抓取过程可能需要较长时间，取决于产品数量和网络状况
- 建议在网络稳定的环境下使用
- 产品筛选基于Amazon搜索结果分析
- 支持中断和恢复，数据安全有保障

## 版本历史

### v1.2 (最新)
- ✅ 100页智能抓取
- ✅ 实时Excel写入
- ✅ 暂停/继续抓取
- ✅ 详细进度显示
- ✅ 状态持久化
- ✅ 智能按钮管理

### v1.1
- ✅ 基础FBM检测
- ✅ CSV导出功能
- ✅ 多种表格适配

### v1.0
- ✅ 初始版本发布 